<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="昵称" prop="nickName">
        <el-input v-model="queryParams.nickName" placeholder="请输入姓名" clearable style="width: 200px"
          @keyup.enter="handleQuery" />
      </el-form-item>
      <el-form-item label="手机号码" prop="phoneNumber">
        <el-input v-model="queryParams.phoneNumber" placeholder="请输入手机号码" clearable style="width: 200px"
          @keyup.enter="handleQuery" />
      </el-form-item>
      <el-form-item label="车牌号" prop="plateNo">
        <el-input v-model="queryParams.plateNo" placeholder="请输入车牌号" clearable style="width: 200px"
          @keyup.enter="handleQuery" />
      </el-form-item>
      <el-form-item label="用户类型" prop="userType">
        <el-select v-model="queryParams.userType" placeholder="请选择用户类型" clearable style="width: 200px">
          <el-option label="VIP客户" value="VIP客户" />
          <el-option label="集团客户" value="集团客户" />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="primary" plain icon="Plus" @click="handleAdd" v-hasPermi="['special:user:add']">新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="success" plain icon="Edit" :disabled="single" @click="handleUpdate"
          v-hasPermi="['special:user:edit']">修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="danger" plain icon="Delete" :disabled="multiple" @click="handleDelete"
          v-hasPermi="['special:user:remove']">删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="warning" plain icon="Download" @click="handleExport"
          v-hasPermi="['special:user:export']">导出</el-button>
      </el-col>
      <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="specialUserList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="昵称" align="center" prop="nickName"/>
      <el-table-column label="手机号码" align="center" prop="phoneNumber"/>
      <el-table-column label="用户类型" align="center" prop="userType">
        <template #default="scope">
          <el-tag :type="getUserTypeTagType(scope.row.userType)">
            {{ scope.row.userType }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="车牌号" align="center" prop="plateNo"/>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template #default="scope">
          <el-button link type="primary" icon="View" @click="handleView(scope.row)"
            v-hasPermi="['special:user:query']">查看</el-button>
          <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)"
            v-hasPermi="['special:user:edit']">修改</el-button>
          <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)"
            v-hasPermi="['special:user:remove']">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <CustomPagination :total="total" v-model:current-page="queryParams.pageNum" v-model:page-size="queryParams.pageSize"
      @pagination="getList" />

    <!-- 添加或修改特殊会员对话框 -->
    <el-dialog
      :title="title"
      v-model="open"
      width="600px"
      append-to-body
      :close-on-click-modal="false"
      :close-on-press-escape="false"
    >
      <el-form ref="specialUserRef" :model="form" :rules="rules" label-width="100px">
        <el-form-item label="姓名" prop="nickName">
          <el-input v-model="form.nickName" placeholder="请输入真实姓名" />
        </el-form-item>
        <el-form-item label="手机号码" prop="phoneNumber">
          <el-input v-model="form.phoneNumber" placeholder="请输入手机号码" />
        </el-form-item>
        <el-form-item label="用户类型" prop="userType">
          <el-select v-model="form.userType" placeholder="请选择用户类型" style="width: 100%">
            <el-option label="VIP客户" value="VIP客户" />
            <el-option label="集团客户" value="集团客户" />
          </el-select>
        </el-form-item>
        <el-form-item label="车牌号" prop="plateNo">
          <el-input v-model="form.plateNo" placeholder="请输入车牌号" />
        </el-form-item>
      </el-form>

      <!-- 操作说明 -->
      <div class="form-notice">
        <el-alert
          type="info"
          :closable="false"
          show-icon>
          <template #default>
            <span v-if="!form.id">
              注：添加特殊会员时，会将系统中该手机号用户变为对应的特殊用户，如果该用户不存在，会新增失败，请确保用户已在系统中注册。
            </span>
            <span v-else>
              注：如果只修改用户类型，系统会将该手机号对应的用户改为对应的特殊会员类型。如果修改手机号，系统会将该手机号对应的用户手机号，
              和对应的特殊会员车辆的手机号一起修改，请谨慎操作。
            </span>
          </template>
        </el-alert>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 查看特殊会员详情对话框 -->
    <el-dialog title="特殊会员详情" v-model="viewOpen" width="600px" append-to-body>
      <el-descriptions :column="2" border>
        <el-descriptions-item label="ID">
          {{ viewData?.id || '-' }}
        </el-descriptions-item>
        <el-descriptions-item label="姓名">
          {{ viewData?.nickName || '-' }}
        </el-descriptions-item>
        <el-descriptions-item label="手机号码">
          {{ viewData?.phoneNumber || '-' }}
        </el-descriptions-item>
        <el-descriptions-item label="车牌号">
          {{ viewData?.plateNo || '-' }}
        </el-descriptions-item>
        <el-descriptions-item label="用户类型" :span="2">
          {{ viewData?.userType || '-' }}
        </el-descriptions-item>
        <el-descriptions-item label="创建者">
          {{ viewData?.createByName || '-' }}
        </el-descriptions-item>
        <el-descriptions-item label="更新者">
          {{ viewData?.updateByName || '-' }}
        </el-descriptions-item>
        <el-descriptions-item label="创建时间">
          {{ parseTime(viewData?.createTime) }}
        </el-descriptions-item>
        <el-descriptions-item label="更新时间">
          {{ parseTime(viewData?.updateTime) }}
        </el-descriptions-item>
      </el-descriptions>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="viewOpen = false">关 闭</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="SpecialUser">
import {
  listSpecialUser,
  getSpecialUser,
  delSpecialUser,
  addSpecialUser,
  updateSpecialUser
} from "@/api/vip/specialUser";
import CustomPagination from "@/components/CustomPagination/index.vue";

// 注册组件
defineOptions({
  components: {
    CustomPagination,
  },
});

const { proxy } = getCurrentInstance();

const specialUserList = ref([]);
const open = ref(false);
const viewOpen = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const title = ref("");
const viewData = ref({});

const data = reactive({
  form: {},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    nickName: null,
    phoneNumber: null,
    plateNo: null,
    userType: null,
  },
  rules: {
    phoneNumber: [
      { required: true, message: "手机号码不能为空", trigger: "blur" },
      { pattern: /^1[3|4|5|6|7|8|9][0-9]\d{8}$/, message: "请输入正确的手机号码", trigger: "blur" }
    ],
    userType: [
      { required: true, message: "用户类型不能为空", trigger: "blur" },
    ],
  },
});

const { queryParams, form, rules } = toRefs(data);

/** 获取用户类型标签颜色 */
function getUserTypeTagType(userType) {
  const typeMap = {
    'VIP客户': 'warning', // 金黄色
    '集团客户': 'warning', // 金黄色
  };
  return typeMap[userType] || 'info'; // 默认灰色
}

/** 查询特殊会员列表 */
function getList() {
  loading.value = true;
  listSpecialUser(queryParams.value).then((response) => {
    specialUserList.value = response.rows;
    total.value = response.total;
    loading.value = false;
  });
}

// 表单重置
function reset() {
  form.value = {
    id: null,
    nickName: null,
    phoneNumber: null,
    plateNo: null,
    userType: null,
  };
  proxy.resetForm("specialUserRef");
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryRef");
  handleQuery();
}

// 多选框选中数据
function handleSelectionChange(selection) {
  ids.value = selection.map((item) => item.id);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
}

/** 查看按钮操作 */
function handleView(row) {
  const _id = row.id || ids.value;
  getSpecialUser(_id).then((response) => {
    viewData.value = response.data || {};
    viewOpen.value = true;
  }).catch((error) => {
    console.error('获取特殊会员详情失败:', error);
    proxy.$modal.msgError("获取特殊会员详情失败");
  });
}

/** 新增按钮操作 */
function handleAdd() {
  reset();
  open.value = true;
  title.value = "添加特殊会员";
}

/** 修改按钮操作 */
function handleUpdate(row) {
  reset();
  const _id = row.id || ids.value;
  getSpecialUser(_id).then((response) => {
    form.value = response.data || {};
    open.value = true;
    title.value = "修改特殊会员";
  });
}

/** 提交按钮 */
function submitForm() {
  proxy.$refs["specialUserRef"].validate((valid) => {
    if (valid) {
      if (form.value.id != null) {
        updateSpecialUser(form.value).then((response) => {
          proxy.$modal.msgSuccess("修改成功");
          open.value = false;
          getList();
        });
      } else {
        addSpecialUser(form.value).then((response) => {
          proxy.$modal.msgSuccess("新增成功");
          open.value = false;
          getList();
        });
      }
    }
  });
}

/** 取消按钮 */
function cancel() {
  open.value = false;
  reset();
}

/** 删除按钮操作 */
function handleDelete(row) {
  const _ids = row.id || ids.value;
  const userName = row ? row.nickName || row.phoneNumber : '选中的特殊会员';

  const confirmMessage = `删除特殊会员前，请先删除该特殊用户所有的特殊会员车辆，才能删除该手机号用户，同时会将该手机号对应的系统用户改为普通用户。

是否确认删除特殊会员"${userName}"？`;

  proxy.$modal.confirm(confirmMessage).then(function () {
    return delSpecialUser(_ids);
  }).then(() => {
    getList();
    proxy.$modal.msgSuccess("删除成功");
  }).catch(() => { });
}

/** 导出按钮操作 */
function handleExport() {
  proxy.download("special/user/export", {
    ...queryParams.value,
  }, `特殊会员_${new Date().getTime()}.xlsx`);
}

onMounted(() => {
  getList();
});
</script>

<style lang="scss" scoped>
.form-notice {
  margin: 20px 0;
}
</style>
