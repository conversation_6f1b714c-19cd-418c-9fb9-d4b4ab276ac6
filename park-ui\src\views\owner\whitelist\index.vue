<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="车牌号" prop="plateNo">
        <el-input
          v-model="queryParams.plateNo"
          placeholder="请输入车牌号"
          clearable
          style="width: 200px"
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="姓名" prop="name">
        <el-input
          v-model="queryParams.name"
          placeholder="请输入姓名"
          clearable
          style="width: 200px"
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="手机号" prop="phoneNumber">
        <el-input
          v-model="queryParams.phoneNumber"
          placeholder="请输入手机号"
          clearable
          style="width: 200px"
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="运营商" prop="operatorId">
        <el-select
          v-model="queryParams.operatorId"
          placeholder="请选择运营商"
          clearable
          style="width: 200px"
          @change="handleQueryOperatorChange"
        >
          <el-option
            v-for="operator in operatorOptions"
            :key="operator.id"
            :label="operator.companyName"
            :value="operator.id"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="场库" prop="warehouseId">
        <el-select
          v-model="queryParams.warehouseId"
          placeholder="请选择场库"
          clearable
          style="width: 200px"
        >
          <el-option
            v-for="warehouse in warehouseOptions"
            :key="warehouse.id"
            :label="warehouse.warehouseName"
            :value="warehouse.id"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="停放区域" prop="parkType">
        <el-select
          v-model="queryParams.parkType"
          placeholder="请选择停放区域类型"
          clearable
          style="width: 200px"
        >
          <el-option
            v-for="item in parkTypeOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>

      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="Plus"
          @click="handleAdd"
          v-hasPermi="['owner:whitelist:add']"
          :disabled="submitLoading || deleteLoading"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="Edit"
          :disabled="single || submitLoading || deleteLoading"
          @click="handleUpdate"
          v-hasPermi="['owner:whitelist:edit']"
        >修改</el-button>
      </el-col>
      <!-- <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="Delete"
          :disabled="multiple || deleteLoading"
          :loading="deleteLoading"
          @click="handleDelete"
          v-hasPermi="['owner:whitelist:remove']"
        >删除</el-button>
      </el-col> -->
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="Download"
          @click="handleExport"
          v-hasPermi="['owner:whitelist:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="whitelistList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="运营商" align="center" prop="operatorName" />
      <el-table-column label="场库名称" align="center" prop="warehouseName" />
      <el-table-column label="车牌号" align="center" prop="plateNo">
        <template #default="scope">
          <el-tag
            :type="getPlateNoTagType(scope.row.plateNo)"
            :color="getPlateNoColor(scope.row.plateNo)"
            effect="plain"
          >
            {{ scope.row.plateNo }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="姓名" align="center" prop="name" />
      <el-table-column label="手机号" align="center" prop="phoneNumber" />
      <el-table-column label="停放区域类型" align="center" prop="parkType">
        <template #default="scope">
          <el-tag
            :type="scope.row.parkType === 0 || scope.row.parkType === '0' ? 'success' : 'primary'"
            size="small"
          >
            {{ scope.row.parkType === 0 || scope.row.parkType === '0' ? '地面' : '地库和地面' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="白名单类型" align="center" prop="whiteType" />
      <el-table-column label="开始时间" align="center" prop="beginTime" width="180">
        <template #default="scope">
          <span>{{ parseTime(scope.row.beginTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="结束时间" align="center" prop="endTime" width="180">
        <template #default="scope">
          <span>{{ parseTime(scope.row.endTime, '{y}-{m}-{d}') }} 23:59:59</span>
        </template>
      </el-table-column>
      <el-table-column label="备注" align="center" prop="remark" />
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template #default="scope">
          <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)" v-hasPermi="['owner:whitelist:edit']" :disabled="deleteLoading">修改</el-button>
          <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)" v-hasPermi="['owner:whitelist:remove']" :loading="deleteLoading">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改白名单管理对话框 -->
    <el-dialog
      :title="title"
      v-model="open"
      width="600px"
      append-to-body
      :close-on-click-modal="false"
      :close-on-press-escape="false"
    >
      <el-form ref="whitelistRef" :model="form" :rules="rules" label-width="100px">
        <el-form-item label="车牌号" prop="plateNo">
          <el-input v-model="form.plateNo" placeholder="请输入车牌号" :disabled="isEdit" />
        </el-form-item>
        <el-form-item label="姓名" prop="name">
          <el-input v-model="form.name" placeholder="请输入姓名" />
        </el-form-item>
        <el-form-item label="手机号" prop="phoneNumber">
          <el-input v-model="form.phoneNumber" placeholder="请输入手机号" />
        </el-form-item>

        <el-form-item label="运营商" prop="selectedOperatorId">
          <el-select
            v-model="form.selectedOperatorId"
            placeholder="请选择运营商"
            clearable
            style="width: 100%"
            @change="handleOperatorChange"
            :disabled="isEdit"
          >
            <el-option
              v-for="operator in operatorOptions"
              :key="operator.id"
              :label="operator.companyName"
              :value="operator.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="场库" prop="warehouseId">
          <el-select
            v-model="form.warehouseId"
            placeholder="请选择场库"
            clearable
            style="width: 100%"
            :disabled="!form.selectedOperatorId || isEdit"
          >
            <el-option
              v-for="warehouse in warehouseOptions"
              :key="warehouse.id"
              :label="warehouse.warehouseName"
              :value="warehouse.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="停放类型" prop="parkType">
          <el-select
            v-model="form.parkType"
            placeholder="请选择停放区域类型"
            clearable
            style="width: 100%"
          >
            <el-option
              v-for="item in parkTypeOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="白名单类型" prop="whiteType">
          <el-select
            v-model="form.whiteType"
            placeholder="请选择白名单类型"
            clearable
            style="width: 100%"
          >
            <el-option
              v-for="item in whiteTypeOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="开始时间" prop="beginTime">
          <el-input
            v-model="form.beginTimeDisplay"
            placeholder="开始时间"
            disabled
            style="width: 100%"
          />
        </el-form-item>
        <el-form-item label="结束时间" prop="endTime">
          <el-date-picker
            v-model="form.endTime"
            type="date"
            placeholder="选择结束日期"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
            :disabled-date="disabledEndDate"
            style="width: 100%"
          />
          <div v-if="form.beginTimeDisplay && form.endTime" class="time-display">
            时间：{{ form.beginTimeDisplay }} 00:00:00 - {{ form.endTime }} 23:59:59
          </div>
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="form.remark" type="textarea" placeholder="请输入内容" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm" :loading="submitLoading">确 定</el-button>
          <el-button @click="cancel" :disabled="submitLoading">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="Whitelist">
import { listWhitelist, getWhitelist, delWhitelist, addWhitelist, updateWhitelist, getWarehouseOptions, getOperatorOptions, getWarehouseOptionsByOperator } from "@/api/owner/whitelist";

const { proxy } = getCurrentInstance();

const whitelistList = ref([]);
const warehouseOptions = ref([]);
const operatorOptions = ref([]);
const open = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const title = ref("");
const isEdit = ref(false); // 是否为修改模式
const submitLoading = ref(false); // 提交加载状态
const deleteLoading = ref(false); // 删除加载状态

// 停放区域类型选项
const parkTypeOptions = ref([
  { label: '地面', value: 0, elTagType: 'success' },
  { label: '地库和地面', value: 1, elTagType: 'primary' }
]);

// 白名单类型选项
const whiteTypeOptions = ref([
  { label: '集团车辆', value: '集团车辆' },
  { label: '特殊优惠', value: '特殊优惠' }
]);

const data = reactive({
  form: {},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    plateNo: null,
    name: null,
    phoneNumber: null,
    operatorId: null,
    warehouseId: null,
    parkType: null
  },
  rules: {
    plateNo: [
      { required: true, message: "车牌号不能为空", trigger: "blur" }
    ],
    name: [
      { required: true, message: "姓名不能为空", trigger: "blur" }
    ],
    phoneNumber: [
      { required: true, message: "手机号不能为空", trigger: "blur" },
      { pattern: /^1[3-9]\d{9}$/, message: "请输入正确的手机号", trigger: "blur" }
    ],
    operatorId: [
      { required: true, message: "运营商不能为空", trigger: "change" }
    ],
    selectedOperatorId: [
      { required: true, message: "运营商不能为空", trigger: "change" }
    ],
    warehouseId: [
      { required: true, message: "场库不能为空", trigger: "change" }
    ],
    parkType: [
      { required: true, message: "停放区域类型不能为空", trigger: "change" }
    ],

    beginTime: [
      { required: true, message: "开始时间不能为空", trigger: "blur" }
    ],
    endTime: [
      { required: true, message: "结束时间不能为空", trigger: "blur" }
    ]
  }
});

const { queryParams, form, rules } = toRefs(data);

/** 查询白名单管理列表 */
function getList() {
  loading.value = true;
  listWhitelist(queryParams.value).then(response => {
    whitelistList.value = response.rows;
    total.value = response.total;
    loading.value = false;
  });
}

/** 获取运营商选项 */
function loadOperatorOptions() {
  getOperatorOptions().then((response) => {
    operatorOptions.value = response.data;
  });
}

/** 获取场库选项 */
function loadWarehouseOptions() {
  getWarehouseOptions().then((response) => {
    warehouseOptions.value = response.data;
  });
}
/** 运营商变化时获取对应场库 */
function handleOperatorChange(operatorId) {
  form.value.warehouseId = null; // 清空场库选择
  warehouseOptions.value = []; // 清空场库选项

  if (operatorId) {
    getWarehouseOptionsByOperator(operatorId).then((response) => {
      warehouseOptions.value = response.data;
    });
  }
}





// 取消按钮
function cancel() {
  open.value = false;
  reset();
}

// 表单重置
function reset() {
  const today = new Date();
  const beginTimeStr = today.getFullYear() + '-' +
    String(today.getMonth() + 1).padStart(2, '0') + '-' +
    String(today.getDate()).padStart(2, '0') + ' 00:00:00';
  const beginDateStr = today.getFullYear() + '-' +
    String(today.getMonth() + 1).padStart(2, '0') + '-' +
    String(today.getDate()).padStart(2, '0');

  form.value = {
    id: null,
    plateNo: null,
    name: null,
    phoneNumber: null,
    selectedOperatorId: null, // 用于选择运营商，不提交到后端
    operatorId: null,
    warehouseId: null,
    parkType: null,
    whiteType: null,
    beginTime: beginTimeStr,
    beginTimeDisplay: beginDateStr, // 只显示日期部分
    endTime: null,
    remark: null
  };
  warehouseOptions.value = []; // 清空场库选项
  isEdit.value = false; // 重置编辑状态
  submitLoading.value = false; // 重置提交加载状态
  proxy.resetForm("whitelistRef");
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryRef");
  handleQuery();
}

// 多选框选中数据
function handleSelectionChange(selection) {
  ids.value = selection.map(item => item.id);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
}

/** 新增按钮操作 */
function handleAdd() {
  reset();
  loadOperatorOptions();
  open.value = true;
  title.value = "添加白名单管理";
}

/** 修改按钮操作 */
function handleUpdate(row) {
  reset();
  const _id = row.id || ids.value;

  getWhitelist(_id).then(response => {
    const whitelistData = response.data;

    form.value = { ...whitelistData };

    // 处理日期时间格式，只显示日期部分
    if (form.value.beginTime) {
      form.value.beginTimeDisplay = form.value.beginTime.split(' ')[0];
      form.value.beginTime = form.value.beginTime;
    }
    if (form.value.endTime) {
      form.value.endTime = form.value.endTime.split(' ')[0];
    }

    // 设置运营商选择（编辑时使用operatorId）
    form.value.selectedOperatorId = form.value.operatorId;

    // 根据运营商加载对应的场库选项
    if (form.value.operatorId) {
      getWarehouseOptionsByOperator(form.value.operatorId).then((warehouseResponse) => {
        warehouseOptions.value = warehouseResponse.data;
      });
    }

    open.value = true;
    title.value = "修改白名单管理";
    isEdit.value = true;
  }).catch((error) => {
    console.error('加载数据失败:', error);
    proxy.$modal.msgError("加载数据失败");
  });
}

/** 提交按钮 */
function submitForm() {
  proxy.$refs["whitelistRef"].validate(valid => {
    if (valid) {
      submitLoading.value = true; // 开始加载

      const submitData = { ...form.value };

      // 移除前端辅助字段，不提交到后端
      delete submitData.beginTimeDisplay;
      delete submitData.selectedOperatorId;

      // 智能拼接日期时间
      if (submitData.beginTime && !submitData.beginTime.includes(' ')) {
        // 开始时间拼接为 00:00:00
        submitData.beginTime = submitData.beginTime + ' 00:00:00';
      }

      if (submitData.endTime && !submitData.endTime.includes(' ')) {
        // 结束时间拼接为 23:59:59
        submitData.endTime = submitData.endTime + ' 23:59:59';
      }

      if (form.value.id != null) {
        updateWhitelist(submitData).then(response => {
          proxy.$modal.msgSuccess("修改成功");
          open.value = false;
          getList();
        }).catch(error => {
          console.error('修改失败:', error);
        }).finally(() => {
          submitLoading.value = false; // 结束加载
        });
      } else {
        addWhitelist(submitData).then(response => {
          proxy.$modal.msgSuccess("新增成功");
          open.value = false;
          getList();
        }).catch(error => {
          console.error('新增失败:', error);
        }).finally(() => {
          submitLoading.value = false; // 结束加载
        });
      }
    }
  });
}

/** 删除按钮操作 */
function handleDelete(row) {
  if (!row || !row.id) {
    proxy.$modal.msgError("请选择要删除的记录");
    return;
  }

  proxy.$modal.confirm('是否确认删除车牌号为"' + row.plateNo + '"的白名单记录？').then(function() {
    deleteLoading.value = true; // 开始删除加载
    return delWhitelist(row.id);
  }).then(() => {
    getList();
    proxy.$modal.msgSuccess("删除成功");
  }).catch((error) => {
    console.error('删除失败:', error);
  }).finally(() => {
    deleteLoading.value = false; // 结束删除加载
  });
}

/** 导出按钮操作 */
function handleExport() {
  proxy.download('system/owner/whitelist/export', {
    ...queryParams.value
  }, `whitelist_${new Date().getTime()}.xlsx`)
}

/** 禁用开始时间选择（不能选择今天之前的日期） */
function disabledBeginDate(time) {
  // 获取今天的日期（设置为00:00:00以便比较）
  const today = new Date();
  today.setHours(0, 0, 0, 0);

  // 禁用今天之前的所有日期
  return time.getTime() < today.getTime();
}

/** 禁用结束时间选择（不能选择开始时间之前的日期） */
function disabledEndDate(time) {
  if (!form.value.beginTime) {
    return false; // 如果没有选择开始时间，则不禁用任何日期
  }

  // 将开始时间转换为Date对象进行比较
  const beginDate = new Date(form.value.beginTime);
  return time.getTime() < beginDate.getTime();
}

/** 获取车牌号标签类型 */
function getPlateNoTagType(plateNo) {
  if (!plateNo) return 'info';
  // 8位为新能源车牌，7位为普通车牌
  return plateNo.length === 8 ? 'success' : 'primary';
}

/** 获取车牌号颜色 */
function getPlateNoColor(plateNo) {
  if (!plateNo) return '#909399';
  // 新能源车牌：浅绿色，普通车牌：浅蓝色
  return plateNo.length === 8 ? '#d4edda' : '#cce7ff';
}

onMounted(() => {
  getList();
  loadOperatorOptions();
  loadWarehouseOptions();
});
</script>
