package com.lgjy.system.api.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.lgjy.common.core.annotation.Excel;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@Data
public class WxUser {

    /** 主键ID */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long id;

    /** 用户账号 */
    @Excel(name = "用户账号")
    private String userName;

    /** 用户昵称 */
    @Excel(name = "用户昵称")
    private String nickName;

    /** 手机号 */
    @Excel(name = "手机号")
    private String phoneNumber;

    /** 密码 */
    private String password;

    /** 微信用户唯一id */
    private String openId;

    /** 用户头像 */
    @Excel(name = "用户头像")
    private String img;

    /** 是否停用 0正常 1停用/封禁 */
    @Excel(name = "状态", readConverterExp = "0=正常,1=停用")
    private Integer status;

    /** 用户类型 0普通用户 1集团客户 2VIP客户 */
    @Excel(name = "用户类型", readConverterExp = "0=普通用户,1=集团客户,2=VIP客户")
    private Integer userType;

    /** 第三方id */
    private String otherId;

    /** 微信余额 */
    private BigDecimal weChatBalance;

    /** 平台余额 */
    private BigDecimal platformBalance;

    /** 赠送余额 */
    private BigDecimal donateBalance;

    /** 删除标志 */
    private Integer deleteFlag;

    /** 创建者 */
    private Long createBy;

    /** 创建时间 */
    private Date createTime;

    /** 更新者 */
    private Long updateBy;

    /** 更新时间 */
    private Date updateTime;
}
