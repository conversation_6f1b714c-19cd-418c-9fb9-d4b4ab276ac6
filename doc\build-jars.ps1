# 捷运停车管理系统 - JAR包批量构建脚本
# PowerShell版本

param(
    [string]$Version = "3.6.6"
)

# 设置控制台编码为UTF-8
[Console]::OutputEncoding = [System.Text.Encoding]::UTF8

Write-Host "========================================" -ForegroundColor Cyan
Write-Host "捷运停车管理系统 - JAR包批量构建脚本" -ForegroundColor Cyan
Write-Host "版本: $Version" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""

# 设置变量
$ScriptDir = Split-Path -Parent $MyInvocation.MyCommand.Path
$BuildDir = Join-Path $ScriptDir "build-output"
$JarsDir = Join-Path $BuildDir "jars"

# 清理并创建输出目录
Write-Host "[1/4] 清理并创建输出目录..." -ForegroundColor Yellow
if (Test-Path $BuildDir) {
    try {
        # 尝试强制删除，如果失败则逐个删除文件
        Remove-Item $BuildDir -Recurse -Force -ErrorAction Stop
    }
    catch {
        Write-Host "  警告: 无法完全删除旧目录，尝试清理文件..." -ForegroundColor Yellow
        # 删除所有文件，但保留目录结构
        Get-ChildItem $BuildDir -Recurse -File | Remove-Item -Force -ErrorAction SilentlyContinue
        # 删除空目录
        Get-ChildItem $BuildDir -Recurse -Directory | Sort-Object FullName -Descending | Remove-Item -Force -ErrorAction SilentlyContinue
    }
}
New-Item -ItemType Directory -Path $BuildDir -Force | Out-Null
New-Item -ItemType Directory -Path $JarsDir -Force | Out-Null

# 执行Maven构建
Write-Host "[2/4] 执行Maven全量构建..." -ForegroundColor Yellow
$mvnResult = & mvn clean package -DskipTests
if ($LASTEXITCODE -ne 0) {
    Write-Host "错误: Maven构建失败!" -ForegroundColor Red
    Read-Host "按任意键退出"
    exit 1
}

# 复制JAR文件
Write-Host "[3/6] 收集JAR文件..." -ForegroundColor Yellow

# 定义服务列表
$services = @(
    @{Name="lgjy-gateway"; Path="lgjy-gateway\target\lgjy-gateway.jar"},
    @{Name="lgjy-auth"; Path="lgjy-auth\target\lgjy-auth.jar"},
    @{Name="lgjy-wx-auth"; Path="lgjy-wx-auth\target\lgjy-wx-auth.jar"},
    @{Name="lgjy-system"; Path="lgjy-modules\lgjy-system\target\lgjy-modules-system.jar"},
    @{Name="lgjy-file"; Path="lgjy-modules\lgjy-file\target\lgjy-modules-file.jar"},
    @{Name="lgjy-wx"; Path="lgjy-modules\lgjy-wx\target\lgjy-modules-wx.jar"},
    @{Name="lgjy-gate"; Path="lgjy-modules\lgjy-gate\target\lgjy-modules-gate.jar"}
)

$successCount = 0
foreach ($service in $services) {
    $sourcePath = Join-Path $ScriptDir $service.Path
    $targetPath = Join-Path $JarsDir "$($service.Name)-$Version.jar"
    
    if (Test-Path $sourcePath) {
        Copy-Item $sourcePath $targetPath
        Write-Host "  ✓ $($service.Name)-$Version.jar" -ForegroundColor Green
        $successCount++
    } else {
        Write-Host "  ✗ $($service.Name).jar 未找到" -ForegroundColor Red
    }
}

Write-Host "成功收集 $successCount 个JAR文件" -ForegroundColor Green

# 复制Dockerfile文件
Write-Host "[4/6] 复制Dockerfile文件..." -ForegroundColor Yellow
$dockerfiles = @(
    @{Source="lgjy-gateway\Dockerfile"; Target="Dockerfile.gateway"},
    @{Source="lgjy-auth\Dockerfile"; Target="Dockerfile.auth"},
    @{Source="lgjy-wx-auth\Dockerfile"; Target="Dockerfile.wx-auth"},
    @{Source="lgjy-modules\lgjy-system\Dockerfile"; Target="Dockerfile.system"},
    @{Source="lgjy-modules\lgjy-file\Dockerfile"; Target="Dockerfile.file"},
    @{Source="lgjy-modules\lgjy-wx\Dockerfile"; Target="Dockerfile.wx"},
    @{Source="lgjy-modules\lgjy-gate\Dockerfile"; Target="Dockerfile.gate"}
)

foreach ($dockerfile in $dockerfiles) {
    $sourcePath = Join-Path $ScriptDir $dockerfile.Source
    $targetPath = Join-Path $DockerfilesDir $dockerfile.Target
    
    if (Test-Path $sourcePath) {
        Copy-Item $sourcePath $targetPath
        Write-Host "  ✓ $($dockerfile.Target)" -ForegroundColor Green
    } else {
        Write-Host "  ✗ $($dockerfile.Source) 未找到" -ForegroundColor Red
    }
}

# 创建通用Dockerfile模板
Write-Host "[5/6] 创建通用Dockerfile模板..." -ForegroundColor Yellow
$dockerfileTemplate = @"
# 通用JAR包Docker镜像构建文件
# 使用方法: docker build -f Dockerfile.template --build-arg JAR_FILE=服务名-$Version.jar -t 镜像名:标签 .

FROM openjdk:8-jre-alpine

# 构建参数
ARG JAR_FILE

WORKDIR /app

# 设置时区
RUN apk add --no-cache tzdata && \
    ln -sf /usr/share/zoneinfo/Asia/Shanghai /etc/localtime && \
    echo "Asia/Shanghai" > /etc/timezone

# 复制JAR文件
COPY jars/`${JAR_FILE} application.jar

# 暴露端口
EXPOSE 8080

# 设置JVM参数
ENV JAVA_OPTS="-Xms512m -Xmx1024m -Djava.security.egd=file:/dev/./urandom"

# 启动应用
ENTRYPOINT ["sh", "-c", "java `$JAVA_OPTS -jar application.jar"]
"@

$dockerfileTemplate | Out-File -FilePath (Join-Path $BuildDir "Dockerfile.template") -Encoding UTF8

# 复制Linux Docker构建脚本
Write-Host "[5/6] 复制Linux Docker构建脚本..." -ForegroundColor Yellow
$buildDockerSource = Join-Path $ScriptDir "build-docker.sh"
$buildDockerTarget = Join-Path $BuildDir "build-docker.sh"

if (Test-Path $buildDockerSource) {
    Copy-Item $buildDockerSource $buildDockerTarget
    Write-Host "  ✓ build-docker.sh" -ForegroundColor Green
} else {
    Write-Host "  ✗ build-docker.sh 未找到" -ForegroundColor Red
}


# 创建简化的部署脚本（保持向后兼容）
$deployScript = @"
#!/bin/bash
# 捷运停车管理系统 - 快速部署脚本（构建所有镜像）

echo "快速构建所有Docker镜像..."
./build-docker.sh --all

echo ""
echo "部署完成！"
echo "使用 'docker-compose up -d' 启动所有服务"
"@

# 使用UTF8NoBOM编码写入文件，确保Linux兼容性
$utf8NoBom = New-Object System.Text.UTF8Encoding $false
[System.IO.File]::WriteAllText((Join-Path $BuildDir "deploy.sh"), $deployScript, $utf8NoBom)

# 复制docker-compose.yml文件
Write-Host "[5.5/6] 复制docker-compose.yml文件..." -ForegroundColor Yellow
Copy-Item (Join-Path $ScriptDir "docker-compose.yml") (Join-Path $BuildDir "docker-compose.yml")
Write-Host "  ✓ docker-compose.yml" -ForegroundColor Green

# 创建MySQL初始化脚本目录和文件（已注释 - 不自动初始化数据库）
# Write-Host "[5.6/6] 创建MySQL初始化脚本..." -ForegroundColor Yellow
# $sqlDir = Join-Path $BuildDir "sql"
# New-Item -ItemType Directory -Path $sqlDir -Force | Out-Null

# 创建数据库初始化脚本（已注释 - 避免自动创建数据库）
# $initScript = @"
# -- MySQL数据库初始化脚本
# -- 作者: 捷运停车管理系统
# -- 说明: 此脚本会自动创建所需的数据库，然后导入数据

# -- 检查并创建parknew数据库
# CREATE DATABASE IF NOT EXISTS parknew CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

# -- 检查并创建nacos数据库
# CREATE DATABASE IF NOT EXISTS nacos CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

# -- 显示创建结果
# SHOW DATABASES;

# -- 提示信息
# SELECT 'Database initialization completed. parknew and nacos databases are ready.' AS Status;
# "@

# $initScript | Out-File -FilePath (Join-Path $sqlDir "01-init-databases.sql") -Encoding UTF8
# Write-Host "  ✓ 数据库初始化脚本" -ForegroundColor Green

# 复制parknew.sql文件（已注释 - 不自动导入数据）
# $parknewSqlSource = Join-Path $ScriptDir "sql\parknew.sql"
# $parknewSqlTarget = Join-Path $sqlDir "02-parknew.sql"
# if (Test-Path $parknewSqlSource) {
#     Copy-Item $parknewSqlSource $parknewSqlTarget
#     Write-Host "  ✓ parknew.sql数据文件" -ForegroundColor Green
# } else {
#     Write-Host "  ✗ parknew.sql文件未找到: $parknewSqlSource" -ForegroundColor Red
# }

# 复制nacos.sql文件（已注释 - 不自动导入数据）
# $nacosSqlSource = Join-Path $ScriptDir "sql\nacos.sql"
# $nacosSqlTarget = Join-Path $sqlDir "03-nacos.sql"
# if (Test-Path $nacosSqlSource) {
#     Copy-Item $nacosSqlSource $nacosSqlTarget
#     Write-Host "  ✓ nacos.sql数据文件" -ForegroundColor Green
# } else {
#     Write-Host "  ✗ nacos.sql文件未找到: $nacosSqlSource" -ForegroundColor Red
# }

Write-Host "[5.6/6] MySQL初始化已跳过（手动配置）..." -ForegroundColor Yellow
Write-Host "  ℹ️ MySQL容器将启动空数据库，需要手动创建数据库和导入数据" -ForegroundColor Cyan



# 创建压缩包
Write-Host "[6/6] 创建压缩包..." -ForegroundColor Yellow
$zipPath = Join-Path $ScriptDir "park-backend-deploy-$Version.zip"
if (Test-Path $zipPath) {
    Remove-Item $zipPath -Force
}

Compress-Archive -Path "$BuildDir\*" -DestinationPath $zipPath -CompressionLevel Optimal

Write-Host ""
Write-Host "========================================" -ForegroundColor Cyan
Write-Host "构建完成!" -ForegroundColor Green
Write-Host "========================================" -ForegroundColor Cyan
Write-Host "输出目录: $BuildDir" -ForegroundColor White
Write-Host "压缩包: $zipPath" -ForegroundColor White
Write-Host ""
Write-Host "部署说明:" -ForegroundColor Yellow
Write-Host "1. 将压缩包上传到Linux服务器" -ForegroundColor White
Write-Host "2. 解压: unzip park-backend-deploy-$Version.zip" -ForegroundColor White
Write-Host "3. 进入目录: cd build-output" -ForegroundColor White
Write-Host "4. 设置执行权限: chmod +x *.sh" -ForegroundColor White
Write-Host "5. 构建镜像选项:" -ForegroundColor White
Write-Host "   - 构建所有: ./build-docker.sh --all" -ForegroundColor Cyan
Write-Host "   - 构建指定: ./build-docker.sh --service wx" -ForegroundColor Cyan
Write-Host "   - 查看服务: ./build-docker.sh --list" -ForegroundColor Cyan
Write-Host "   - 快速部署: ./deploy.sh" -ForegroundColor Cyan
Write-Host "6. 启动服务: docker-compose up -d" -ForegroundColor White
Write-Host "7. 查看状态: docker-compose ps" -ForegroundColor White
Write-Host ""
Write-Host "⚠️  数据库初始化说明:" -ForegroundColor Red
Write-Host "MySQL容器启动后需要手动初始化数据库:" -ForegroundColor Yellow
Write-Host "1. 连接MySQL: docker exec -it park-mysql mysql -uroot -p1234" -ForegroundColor White
Write-Host "2. 创建数据库: CREATE DATABASE parknew; CREATE DATABASE nacos;" -ForegroundColor White
Write-Host "3. 导入数据: 手动执行parknew.sql和nacos.sql文件" -ForegroundColor White
Write-Host ""
Read-Host "按任意键退出"
