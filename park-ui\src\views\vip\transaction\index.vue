<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="手机号码" prop="phoneNumber">
        <el-input v-model="queryParams.phoneNumber" placeholder="请输入手机号码" clearable style="width: 200px"
          @keyup.enter="handleQuery" />
      </el-form-item>
      <el-form-item label="车牌号" prop="plateNo">
        <el-input v-model="queryParams.plateNo" placeholder="请输入车牌号" clearable style="width: 200px"
          @keyup.enter="handleQuery" />
      </el-form-item>
      <el-form-item label="场库" prop="warehouseId">
        <el-cascader
          v-model="queryParams.warehouseId"
          :options="warehouseCascaderOptions"
          :props="{
            value: 'value',
            label: 'label',
            children: 'children',
            emitPath: false,
            checkStrictly: true,
            expandTrigger: 'hover'
          }"
          placeholder="请选择场库或停车场"
          style="width: 200px"
          clearable
          filterable
          :show-all-levels="false"
        />
      </el-form-item>
      <el-form-item label="套餐类型" prop="packageId">
        <el-select v-model="queryParams.packageId" placeholder="套餐类型" clearable style="width: 200px">
          <el-option v-for="dict in vip_package_type" :key="dict.value" :label="dict.label"
            :value="parseInt(dict.value)" />
        </el-select>
      </el-form-item>
      <el-form-item label="支付状态" prop="payStatus">
        <el-select v-model="queryParams.payStatus" placeholder="支付状态" clearable style="width: 200px">
          <el-option v-for="dict in pay_status" :key="dict.value" :label="dict.label" :value="parseInt(dict.value)" />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="primary" plain icon="Plus" @click="handleAdd"
          v-hasPermi="['vip:transaction:add']">新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="success" plain icon="Edit" :disabled="single" @click="handleUpdate"
          v-hasPermi="['vip:transaction:edit']">修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="danger" plain icon="Delete" :disabled="multiple" @click="handleDelete"
          v-hasPermi="['vip:transaction:remove']">删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="warning" plain icon="Download" @click="handleExport"
          v-hasPermi="['vip:transaction:export']">导出</el-button>
      </el-col>
      <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="transactionList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <!-- <el-table-column label="交易ID" align="center" prop="id" /> -->
      <el-table-column label="订单号" align="center" prop="tradeId" />
      <el-table-column label="手机号" align="center" prop="phoneNumber" width="130">
        <template #default="scope">
          {{ scope.row.phoneNumber }}
        </template>
      </el-table-column>
      <el-table-column label="车牌号" align="center" prop="plateNo" width="120">
        <template #default="scope">
          <el-tag
            :type="getPlateNoTagType(scope.row.plateNo)"
            :color="getPlateNoColor(scope.row.plateNo)"
            effect="plain"
          >
            {{ scope.row.plateNo }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="场库" align="center" prop="warehouseName" width="140">
        <template #default="scope">
          <el-tag type="primary" effect="light" size="small">
            <i class="el-icon-office-building" style="margin-right: 4px;"></i>
            {{ scope.row.warehouseName }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="套餐名称" align="center" prop="packageName" width="140">
        <template #default="scope">
          <el-tag v-if="scope.row.packageName" type="warning" effect="light" size="small">
            <i class="el-icon-box" style="margin-right: 4px;"></i>
            {{ scope.row.packageName }}
          </el-tag>
          <span v-else style="color: #909399;">未知套餐</span>
        </template>
      </el-table-column>
      <el-table-column label="会员类型" align="center" prop="vipType" width="100">
        <template #default="scope">
          <dict-tag
            :options="vip_member_type"
            :value="scope.row.vipType"
          />
        </template>
      </el-table-column>
      <el-table-column label="应付金额" align="center" prop="paymentAmount" width="100">
        <template #default="scope">
          <span class="price-text">¥{{ scope.row.paymentAmount || '0.00' }}</span>
        </template>
      </el-table-column>
      <el-table-column label="实际支付" align="center" prop="actualPayment" width="100">
        <template #default="scope">
          <span class="actual-payment-text">¥{{ scope.row.actualPayment || '0.00' }}</span>
        </template>
      </el-table-column>
      <el-table-column label="支付状态" align="center" prop="payStatus" width="100">
        <template #default="scope">
          <dict-tag :options="pay_status" :value="scope.row.payStatus" />
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width" fixed="right" width="180">
        <template #default="scope">
          <el-button link type="primary" icon="View" @click="handleView(scope.row)"
            v-hasPermi="['vip:transaction:query']">详情</el-button>
          <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)"
            v-hasPermi="['vip:transaction:edit']">修改</el-button>
          <el-button link type="danger" icon="RefreshLeft" @click="handleRefund(scope.row)"
            v-hasPermi="['vip:transaction:refund']" v-if="scope.row.payStatus === 5">退款</el-button>
        </template>
      </el-table-column>
    </el-table>

    <custom-pagination :total="total" v-model:current-page="queryParams.pageNum"
      v-model:page-size="queryParams.pageSize" @pagination="getList" />

    <!-- 添加或修改会员交易记录对话框 -->
    <el-dialog
      :title="title"
      v-model="open"
      width="800px"
      append-to-body
      :close-on-click-modal="false"
      :close-on-press-escape="false"
    >
      <!-- 查看模式：美化展示 -->
      <div v-if="isViewMode" class="transaction-detail-view">
        <el-card class="detail-card" shadow="never">
          <template #header>
            <div class="card-header">
              <span class="header-title">交易信息</span>
              <el-tag type="success" size="small">交易编号：{{ form.id }}</el-tag>
            </div>
          </template>

          <el-descriptions :column="2" border>
            <el-descriptions-item label="交易ID">
              <el-tag type="primary" size="small">{{ form.id }}</el-tag>
            </el-descriptions-item>
            <el-descriptions-item label="交易流水号">
              {{ form.tradeId || '无' }}
            </el-descriptions-item>
            <el-descriptions-item label="停车场">
              {{ getWarehouseName(form.warehouseId) }}
            </el-descriptions-item>
            <el-descriptions-item label="手机号码">
              <el-tag v-if="form.phoneNumber" type="info" effect="plain" size="small">
                <i class="el-icon-phone" style="margin-right: 4px;"></i>
                {{ form.phoneNumber }}
              </el-tag>
              <span v-else style="color: #909399;">无</span>
            </el-descriptions-item>
            <el-descriptions-item label="车牌号">
              <el-tag
                v-if="form.plateNo"
                :type="getPlateNoTagType(form.plateNo)"
                :color="getPlateNoColor(form.plateNo)"
                effect="plain"
              >
                {{ form.plateNo }}
              </el-tag>
              <span v-else style="color: #909399;">无</span>
            </el-descriptions-item>
            <el-descriptions-item label="套餐名称">
              {{ form.packageName || '未知套餐' }}
            </el-descriptions-item>
            <el-descriptions-item label="支付状态">
              <dict-tag :options="pay_status" :value="form.payStatus" />
            </el-descriptions-item>
            <el-descriptions-item label="VIP开始时间">
              <div style="display: flex; align-items: center;">
                <i class="el-icon-time" style="color: #67C23A; margin-right: 4px;"></i>
                <span>{{ parseTime(form.beginVipTime, '{y}-{m}-{d} {h}:{i}:{s}') || '-' }}</span>
              </div>
            </el-descriptions-item>
          </el-descriptions>
        </el-card>

        <el-card class="detail-card" shadow="never">
          <template #header>
            <div class="card-header">
              <span class="header-title">金额信息</span>
            </div>
          </template>

          <el-descriptions :column="2" border>
            <el-descriptions-item label="应付金额">
              <span class="amount-text">¥{{ form.paymentAmount || '0.00' }}</span>
            </el-descriptions-item>
            <el-descriptions-item label="实际支付">
              <span class="actual-amount-text">¥{{ form.actualPayment || '0.00' }}</span>
            </el-descriptions-item>
            <el-descriptions-item label="优惠金额">
              ¥{{ form.discountAmount || '0.00' }}
            </el-descriptions-item>
            <el-descriptions-item label="创建时间">
              <div style="display: flex; align-items: center;">
                <i class="el-icon-time" style="color: #909399; margin-right: 4px;"></i>
                <span>{{ parseTime(form.createTime, '{y}-{m}-{d} {h}:{i}:{s}') || '-' }}</span>
              </div>
            </el-descriptions-item>
          </el-descriptions>
        </el-card>

        <el-card class="detail-card" shadow="never" v-if="form.remark">
          <template #header>
            <div class="card-header">
              <span class="header-title">备注信息</span>
            </div>
          </template>

          <el-descriptions :column="1" border>
            <el-descriptions-item label="备注">
              <div class="description-text">{{ form.remark || '-' }}</div>
            </el-descriptions-item>
          </el-descriptions>
        </el-card>
      </div>

      <!-- 编辑模式：表单 -->
      <el-form v-else ref="transactionRef" :model="form" :rules="rules" label-width="100px">
        <el-row>
          <el-col :span="12">
            <el-form-item label="停车场" prop="warehouseId">
              <el-select v-model="form.warehouseId" placeholder="请选择停车场" filterable remote reserve-keyword
                :remote-method="searchWarehouses" :loading="warehouseLoading" style="width: 100%">
                <el-option v-for="warehouse in warehouseOptions" :key="warehouse.id"
                  :label="`${warehouse.warehouseName} (ID: ${warehouse.id})`" :value="warehouse.id" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="套餐ID" prop="packageId">
              <el-input-number v-model="form.packageId" placeholder="请输入套餐ID" style="width: 100%" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="手机号码" prop="phoneNumber">
              <el-input v-model="form.phoneNumber" placeholder="请输入手机号码" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="车牌号" prop="plateNo">
              <el-input v-model="form.plateNo" placeholder="请输入车牌号" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="套餐类型" prop="packageId">
              <el-select v-model="form.packageId" placeholder="请选择套餐类型">
                <el-option v-for="dict in vip_package_type" :key="dict.value" :label="dict.label"
                  :value="parseInt(dict.value)" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="支付状态" prop="payStatus">
              <el-select v-model="form.payStatus" placeholder="请选择支付状态">
                <el-option v-for="dict in pay_status" :key="dict.value" :label="dict.label"
                  :value="parseInt(dict.value)" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="操作类型" prop="operateType">
              <el-select v-model="form.operateType" placeholder="请选择操作类型">
                <el-option label="新购" :value="1" />
                <el-option label="续费" :value="2" />
                <el-option label="退款" :value="3" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="交易时间" prop="transactTime">
              <el-date-picker
                v-model="form.transactTime"
                type="datetime"
                placeholder="请选择交易时间"
                format="YYYY-MM-DD HH:mm:ss"
                value-format="YYYY-MM-DD HH:mm:ss"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="会员类型" prop="vipType">
              <el-select v-model="form.vipType" placeholder="请选择会员类型">
                <el-option
                  v-for="dict in vip_member_type"
                  :key="dict.value"
                  :label="dict.label"
                  :value="parseInt(dict.value)"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="应付金额" prop="paymentAmount">
              <el-input-number v-model="form.paymentAmount" :min="0" :precision="2" placeholder="请输入应付金额" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="实际支付" prop="actualPayment">
              <el-input-number v-model="form.actualPayment" :min="0" :precision="2" placeholder="请输入实际支付金额" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="交易ID" prop="tradeId">
              <el-input v-model="form.tradeId" placeholder="请输入交易ID" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="优惠金额" prop="discountAmount">
              <el-input-number v-model="form.discountAmount" :min="0" :precision="2" placeholder="请输入优惠金额" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="form.remark" type="textarea" placeholder="请输入备注" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button v-if="!isViewMode" type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">{{ isViewMode ? '关 闭' : '取 消' }}</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 退款对话框 -->
    <el-dialog title="退款处理" v-model="refundOpen" width="400px" append-to-body>
      <el-form ref="refundRef" :model="refundForm" :rules="refundRules" label-width="100px">
        <el-form-item label="退款金额" prop="refundAmount">
          <el-input-number v-model="refundForm.refundAmount" :min="0" :precision="2" placeholder="请输入退款金额" />
        </el-form-item>
        <el-form-item label="退款原因" prop="refundReason">
          <el-input v-model="refundForm.refundReason" type="textarea" placeholder="请输入退款原因" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitRefund">确 定</el-button>
          <el-button @click="cancelRefund">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="VipTransaction">
import {
  listVipTransaction,
  getVipTransaction,
  delVipTransaction,
  addVipTransaction,
  updateVipTransaction,
  processRefund,
} from "@/api/vip/transaction";
import { listVipMember, getWarehouseOptions } from "@/api/vip/member";
import { listWarehouse } from "@/api/platform/warehouse";
import CustomPagination from "@/components/CustomPagination/index.vue";

// 注册组件
defineOptions({
  components: {
    CustomPagination,
  },
});

const { proxy } = getCurrentInstance();
const { pay_status, vip_package_type, vip_member_type } = proxy.useDict("pay_status", "vip_package_type", "vip_member_type");

const transactionList = ref([]);
const open = ref(false);
const refundOpen = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const title = ref("");
const isViewMode = ref(false);

// 停车场和会员选择相关
const warehouseOptions = ref([]);
const warehouseCascaderOptions = ref([]);
const memberOptions = ref([]);
const warehouseLoading = ref(false);
const memberLoading = ref(false);

// 移除硬编码选项，改用字典

const data = reactive({
  form: {},
  refundForm: {},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    phoneNumber: null,
    plateNo: null,
    warehouseId: null,
    packageId: null,
    payStatus: null,
  },
  rules: {
    warehouseId: [
      { required: true, message: "停车场不能为空", trigger: "change" },
    ],
    packageId: [
      { required: true, message: "套餐ID不能为空", trigger: "blur" },
    ],
    phoneNumber: [
      { required: true, message: "手机号码不能为空", trigger: "blur" },
    ],
    plateNo: [
      { required: true, message: "车牌号不能为空", trigger: "blur" },
    ],
    packageId: [
      { required: true, message: "套餐类型不能为空", trigger: "change" },
    ],
    operateType: [
      { required: true, message: "操作类型不能为空", trigger: "change" },
    ],
    transactTime: [
      { required: true, message: "交易时间不能为空", trigger: "change" },
    ],
    paymentAmount: [
      { required: true, message: "应付金额不能为空", trigger: "blur" },
    ],
  },
  refundRules: {
    refundAmount: [
      { required: true, message: "退款金额不能为空", trigger: "blur" },
    ],
    refundReason: [
      { required: true, message: "退款原因不能为空", trigger: "blur" },
    ],
  },
});

const { queryParams, form, rules, refundForm, refundRules } = toRefs(data);

// 移除硬编码状态处理函数，改用字典标签



// 搜索停车场
function searchWarehouses(query) {
  if (query !== '') {
    warehouseLoading.value = true;
    const searchParams = {
      warehouseName: query,
      pageNum: 1,
      pageSize: 20
    };

    listWarehouse(searchParams).then(response => {
      warehouseOptions.value = response.rows || [];
      warehouseLoading.value = false;
    }).catch(() => {
      warehouseOptions.value = [];
      warehouseLoading.value = false;
    });
  } else {
    warehouseOptions.value = [];
  }
}

// 搜索会员
function searchMembers(query) {
  if (query !== '') {
    memberLoading.value = true;
    const searchParams = {
      memberName: query,
      phone: query,
      pageNum: 1,
      pageSize: 20
    };

    listVipMember(searchParams).then(response => {
      memberOptions.value = response.rows || [];
      memberLoading.value = false;
    }).catch(() => {
      memberOptions.value = [];
      memberLoading.value = false;
    });
  } else {
    memberOptions.value = [];
  }
}

// 初始化加载停车场和会员选项
function initOptions() {
  // 加载默认停车场列表
  searchWarehouses('');
  // 加载默认会员列表
  searchMembers('');
}

/** 获取场库选项 */
function loadWarehouseOptions() {
  getWarehouseOptions().then((response) => {
    warehouseOptions.value = response.data;
    // 同时构建层级选项（用于cascader）
    warehouseCascaderOptions.value = buildWarehouseCascaderOptions(response.data);
  });
}

/** 构建级联选择器选项 */
function buildWarehouseCascaderOptions(warehouses) {
  if (!warehouses || warehouses.length === 0) {
    return [];
  }

  // 分离主场库和子场库
  const mainWarehouses = warehouses.filter(w => w.parentId === "0");
  const subWarehouses = warehouses.filter(w => w.parentId !== "0");

  // 构建级联结构
  return mainWarehouses.map(mainWarehouse => {
    const children = subWarehouses
      .filter(sub => sub.parentId === mainWarehouse.id)
      .map(sub => ({
        value: sub.id,
        label: sub.warehouseName,
        isLeaf: true
      }));

    return {
      value: mainWarehouse.id,
      label: mainWarehouse.warehouseName,
      children: children.length > 0 ? children : undefined
    };
  });
}

// 获取停车场名称
function getWarehouseName(warehouseId) {
  const warehouse = warehouseOptions.value.find(w => w.id === warehouseId);
  return warehouse ? warehouse.warehouseName : `停车场ID: ${warehouseId}`;
}

// 获取车牌号标签类型
function getPlateNoTagType(plateNo) {
  if (!plateNo) return 'info';
  // 8位为新能源车牌，7位为普通车牌
  return plateNo.length === 8 ? 'success' : 'primary';
}

// 获取车牌号颜色
function getPlateNoColor(plateNo) {
  if (!plateNo) return '#909399';
  // 新能源车牌：浅绿色，普通车牌：浅蓝色
  return plateNo.length === 8 ? '#d4edda' : '#cce7ff';
}





/** 查询会员交易记录列表 */
function getList() {
  loading.value = true;
  listVipTransaction(queryParams.value).then((response) => {
    transactionList.value = response.rows;
    total.value = response.total;
    loading.value = false;
  });
}

// 取消按钮
function cancel() {
  open.value = false;
  reset();
}

// 表单重置
function reset() {
  form.value = {
    id: null,
    warehouseId: null,
    packageId: null,
    userId: null,
    phoneNumber: null,
    plateNo: null,
    packageId: null,
    operateType: 1,
    transactTime: null,
    beginVipTime: null,
    expirationTime: null,
    paymentAmount: null,
    discountAmount: null,
    actualPayment: null,
    tradeId: null,
    payStatus: 0,
    invoiceId: null,
    parkingSpaceNo: null,
    groupBuyRecordId: null,
    chooseTime: null,
    remark: null,
  };
  proxy.resetForm("transactionRef");
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryRef");
  // 重置查询参数到初始状态
  Object.assign(queryParams.value, {
    pageNum: 1,
    pageSize: 10,
    phoneNumber: null,
    plateNo: null,
    warehouseId: null,
    packageId: null,
    payStatus: null
  });
  handleQuery();
}

// 多选框选中数据
function handleSelectionChange(selection) {
  ids.value = selection.map((item) => item.id);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
}

/** 新增按钮操作 */
function handleAdd() {
  reset();
  isViewMode.value = false;
  // 设置默认值
  form.value.operateType = 1; // 默认为新购
  form.value.transactTime = new Date().toISOString().slice(0, 19).replace('T', ' '); // 当前时间
  initOptions(); // 初始化停车场和会员选项
  open.value = true;
  title.value = "添加会员交易记录";
}

/** 修改按钮操作 */
function handleUpdate(row) {
  reset();
  isViewMode.value = false;
  const _id = row.id || ids.value;
  getVipTransaction(_id).then((response) => {
    form.value = response.data;
    open.value = true;
    title.value = "修改会员交易记录";
  });
}

/** 查看详情 */
function handleView(row) {
  reset();
  isViewMode.value = true;
  getVipTransaction(row.id).then((response) => {
    form.value = response.data;
    // 加载相关的停车场信息用于显示
    if (form.value.warehouseId) {
      searchWarehouses('');
    }
    open.value = true;
    title.value = "查看交易详情";
  });
}

/** 退款处理 */
function handleRefund(row) {
  refundForm.value = {
    id: row.id,
    refundAmount: null,
    refundReason: null,
  };
  refundOpen.value = true;
}

/** 提交退款 */
function submitRefund() {
  proxy.$refs["refundRef"].validate((valid) => {
    if (valid) {
      processRefund(
        refundForm.value.id,
        refundForm.value.refundAmount,
        refundForm.value.refundReason
      ).then((response) => {
        proxy.$modal.msgSuccess("退款处理成功");
        refundOpen.value = false;
        getList();
      });
    }
  });
}

/** 取消退款 */
function cancelRefund() {
  refundOpen.value = false;
  refundForm.value = {};
}

/** 提交按钮 */
function submitForm() {
  proxy.$refs["transactionRef"].validate((valid) => {
    if (valid) {
      if (form.value.id != null) {
        updateVipTransaction(form.value).then((response) => {
          proxy.$modal.msgSuccess("修改成功");
          open.value = false;
          getList();
        });
      } else {
        addVipTransaction(form.value).then((response) => {
          proxy.$modal.msgSuccess("新增成功");
          open.value = false;
          getList();
        });
      }
    }
  });
}

/** 删除按钮操作 */
function handleDelete(row) {
  const _ids = row.id || ids.value;
  proxy.$modal
    .confirm('是否确认删除会员交易记录编号为"' + _ids + '"的数据项？')
    .then(function () {
      return delVipTransaction(_ids);
    })
    .then(() => {
      getList();
      proxy.$modal.msgSuccess("删除成功");
    })
    .catch(() => { });
}

/** 导出按钮操作 */
function handleExport() {
  proxy.download(
    "system/vip/transaction/export",
    {
      ...queryParams.value,
    },
    `vip_transaction_${new Date().getTime()}.xlsx`
  );
}

// 初始化数据
loadWarehouseOptions();
getList();
</script>

<style scoped>
/* VIP交易记录详情查看样式 */
.transaction-detail-view {
  padding: 0;
}

.transaction-detail-view .detail-card {
  margin-bottom: 20px;
  border-radius: 8px;
  border: 1px solid #e4e7ed;
}

.transaction-detail-view .detail-card:last-child {
  margin-bottom: 0;
}

.transaction-detail-view .card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0;
}

.transaction-detail-view .header-title {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.transaction-detail-view .el-descriptions {
  margin-top: 0;
}

.transaction-detail-view .el-descriptions__label {
  font-weight: 500;
  color: #606266;
  background-color: #fafafa;
}

.transaction-detail-view .el-descriptions__content {
  color: #303133;
}

.transaction-detail-view .amount-text {
  font-size: 16px;
  color: #f56c6c;
  font-weight: 600;
}

.transaction-detail-view .actual-amount-text {
  font-size: 16px;
  color: #f56c6c;
  font-weight: 600;
}

.transaction-detail-view .description-text {
  line-height: 1.6;
  color: #303133;
  white-space: pre-wrap;
  word-break: break-word;
}

/* 表格中的金额样式 */
.price-text {
  color: #f56c6c;
  font-weight: 600;
}

.actual-payment-text {
  color: #f56c6c;
  font-weight: 600;
}

/* 表格样式优化 */
.el-table {
  border-radius: 4px;
  overflow: hidden;
}

.el-table th {
  background-color: #fafafa;
  color: #606266;
  font-weight: 500;
}

.el-table td {
  border-bottom: 1px solid #ebeef5;
}

.el-table tr:hover td {
  background-color: #f5f7fa;
}

.el-tag {
  border-radius: 12px;
  font-size: 12px;
  padding: 0 8px;
  height: 24px;
  line-height: 22px;
}
</style>
