package com.lgjy.wx.service;

import com.alibaba.fastjson2.JSONObject;
import com.lgjy.wx.domain.*;

import java.math.BigDecimal;
import java.util.List;

public interface WxPackageService {
    /**
     * 根据场库id和套餐类型查套餐列表(某场库普通套餐列表)
     */
    List<WxPackage> selectWxPackageList(WxPackage wxPackage);

    /**
     * 查询某用户在某场库的某车的未过期套餐（未过期的会员信息）
     */
    WxUserPackage selectWxUserPackage(WxUserPackage wxUserPackage);

    /**
     * 查询用户的套餐购买记录
     */
    List<WxUserPackageRecord> selectWxUserPackageRecordList(WxUserPackageRecord wxUserPackageRecord);

    /**
     * (普通,集团，vip)套餐购买预下单
     */
    JSONObject createOrder(WxUserPackageRecord wxUserPackageRecord);

    /**
     * 小程序普通套餐支付成功，供银联回调(微信)
     */
    void payCallback(JSONObject body);

    /**
     * 购买套餐前，查看车辆是否在场库
     */
    WxPackageJudge packageJudge(WxUserPackage wxUserPackage);

    /**
     * VIP用户查询所有车辆套餐
     */
    List<WxVipCarInfo> getWxVipUserPackageList(Integer vipType);

    int updateOrder(WxUserPackageRecord wxUserPackageRecord);

    /**
     * 会员套餐订单退款
     * @param tradeId 订单号
     * @param refundAmount 退款金额
     * @param refundReason 退款原因
     * @return 退款结果
     */
    JSONObject refundPackageOrder(String tradeId, BigDecimal refundAmount, String refundReason);
}
