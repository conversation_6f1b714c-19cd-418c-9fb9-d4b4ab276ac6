package com.lgjy.wx.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.lgjy.common.core.utils.StringUtils;
import com.lgjy.common.core.web.controller.BaseController;
import com.lgjy.common.core.web.domain.AjaxResult;
import com.lgjy.common.security.annotation.InnerAuth;
import com.lgjy.wx.domain.*;
import com.lgjy.wx.service.WxPackageService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
@RestController
@RequestMapping("/package")
public class WxPackageController extends BaseController {
    @Resource
    private WxPackageService wxPackageService;

    /**
     * 根据场库id查询普通套餐列表
     */
    @PostMapping("/list")
    public AjaxResult selectWxPackageList(@RequestBody WxPackage wxPackage)
    {
        List<WxPackage> list = wxPackageService.selectWxPackageList(wxPackage);
        return success(list);
    }

    /**
     * 查询某用户在某场库的某车的未过期套餐（未过期的会员信息）
     */
    @PostMapping("/user/plate")
    public AjaxResult selectWxUserPackageList(@RequestBody WxUserPackage wxUserPackage)
    {
        return success(wxPackageService.selectWxUserPackage(wxUserPackage));
    }

    /**
     * 查询用户的普通套餐购买记录
     */
    @PostMapping("/user/record/list")
    public AjaxResult selectWxUserPackageRecordList(@RequestBody WxUserPackageRecord wxUserPackageRecord)
    {
        List<WxUserPackageRecord> list = wxPackageService.selectWxUserPackageRecordList(wxUserPackageRecord);
        return success(list);
    }


    /**
     *  (普通,集团，vip)套餐购买预下单
     */
    @PostMapping("/order/create")
    public AjaxResult createOrder(@RequestBody WxUserPackageRecord wxUserPackageRecord) {
        try {
            return success(wxPackageService.createOrder(wxUserPackageRecord));
        } catch (Exception e) {
            log.error("个人套餐办理预下单失败", e);
            return error(e.getMessage());
        }
    }



    /**
     *  (普通,集团，vip)更新订单
     */
    @PostMapping("/order/update")
    public AjaxResult updateOrder(@RequestBody WxUserPackageRecord wxUserPackageRecord) {
        try {
            return success(wxPackageService.updateOrder(wxUserPackageRecord));
        } catch (Exception e) {
            log.error("个人套餐办理预下单失败", e);
            return error(e.getMessage());
        }
    }

    /**
     * 小程序套餐支付成功，供银联回调(微信)
     */
    @PostMapping("/payCallback")
    public Map<String, Object> payCallback(HttpServletRequest request, HttpServletResponse response) {
        JSONObject body = new JSONObject();
        Map<String, String[]> map = request.getParameterMap();
        for (String key : map.keySet()) {
            body.put(key, map.get(key)[0]);
        }
        log.info("停车订单支付回调start---------------body:{}", JSON.toJSONString(body));
        try {
            wxPackageService.payCallback(body);
            log.info("停车订单支付回调end---------------");
        } catch (Exception e) {
            log.error("临停缴费支付回调失败", e);
        }
        Map<String, Object> result = new HashMap<>();
        result.put("code", "SUCCESS");
        result.put("message", "");
        return result;
    }

    /**
     * 购买套餐前，查看车辆是否在场库(按照会员截止，还是临停截止)
     */
    @PostMapping("/packageJudge")
    public AjaxResult packageJudge(@RequestBody WxUserPackage wxUserPackage)
    {
        WxPackageJudge wxPackageJudge = wxPackageService.packageJudge(wxUserPackage);
        return success(wxPackageJudge);
    }

    /**
     * VIP/集团用户页面，查询所有车辆套餐
     */
    @GetMapping("/vip/list/{vipType}")
    public AjaxResult getWxVipUserPackageList(@PathVariable Integer vipType)
    {
        List<WxVipCarInfo> result = wxPackageService.getWxVipUserPackageList(vipType);
        return success(result);
    }

    /**
     * 会员套餐订单退款
     */
    @InnerAuth
    @PostMapping("/order/refund")
    public AjaxResult refundPackageOrder(@RequestParam String tradeId,
                                        @RequestParam BigDecimal refundAmount,
                                        @RequestParam String refundReason) {
        try {
            JSONObject result = wxPackageService.refundPackageOrder(tradeId, refundAmount, refundReason);
            return success("退款成功", result);
        } catch (Exception e) {
            log.error("会员套餐订单退款失败", e);
            return error(e.getMessage());
        }
    }
}
