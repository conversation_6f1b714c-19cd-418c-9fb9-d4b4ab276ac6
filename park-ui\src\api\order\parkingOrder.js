import request from '@/utils/request'

// 查询停车订单列表
export function listParkingOrder(query) {
  return request({
    url: '/system/parkingOrder/list',
    method: 'get',
    params: query
  })
}

// 查询停车订单详细
export function getParkingOrder(id) {
  return request({
    url: '/system/parkingOrder/' + id,
    method: 'get'
  })
}

// 新增停车订单
export function addParkingOrder(data) {
  return request({
    url: '/system/parkingOrder',
    method: 'post',
    data: data
  })
}

// 修改停车订单
export function updateParkingOrder(data) {
  return request({
    url: '/system/parkingOrder',
    method: 'put',
    data: data
  })
}

// 删除停车订单
export function delParkingOrder(id) {
  return request({
    url: '/system/parkingOrder/' + id,
    method: 'delete'
  })
}

// 根据车牌号查询停车订单
export function getParkingOrderByPlateNo(plateNo) {
  return request({
    url: '/system/parkingOrder/plateNo/' + plateNo,
    method: 'get'
  })
}

// 根据场库ID查询停车订单
export function getParkingOrderByWarehouseId(warehouseId) {
  return request({
    url: '/system/parkingOrder/warehouse/' + warehouseId,
    method: 'get'
  })
}

// 根据支付状态查询停车订单
export function getParkingOrderByPayStatus(payStatus) {
  return request({
    url: '/system/parkingOrder/payStatus/' + payStatus,
    method: 'get'
  })
}

// 统计订单数量
export function countParkingOrder(query) {
  return request({
    url: '/system/parkingOrder/count',
    method: 'get',
    params: query
  })
}

// 统计订单金额
export function sumParkingOrderAmount(query) {
  return request({
    url: '/system/parkingOrder/sum',
    method: 'get',
    params: query
  })
}

// 获取车辆类型选项
export function getCarTypeOptions() {
  return request({
    url: '/system/parkingOrder/carTypeOptions',
    method: 'get'
  })
}

// 导出停车订单
export function exportParkingOrder(query) {
  return request({
    url: '/system/parkingOrder/export',
    method: 'post',
    params: query
  })
}

// 停车订单退款
export function refundParkingOrder(data) {
  return request({
    url: '/system/parkingOrder/refund',
    method: 'post',
    data: data
  })
}
