package com.lgjy.wx;

import com.lgjy.common.security.annotation.EnableCustomConfig;
import com.lgjy.common.security.annotation.EnableRyFeignClients;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.scheduling.annotation.EnableScheduling;

/**
 * 小程序系统模块
 *
 * <AUTHOR>
 */
@EnableDiscoveryClient
@EnableCustomConfig
@EnableRyFeignClients
@EnableScheduling
@SpringBootApplication
public class LgjyWxApplication {
    public static void main(String[] args) {
        SpringApplication.run(LgjyWxApplication.class, args);
        System.out.println("微信模块启动成功");
    }
}