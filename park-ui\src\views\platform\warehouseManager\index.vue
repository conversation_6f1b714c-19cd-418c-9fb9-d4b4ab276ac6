<template>
  <div class="app-container">
    <el-form
      :model="queryParams"
      ref="queryRef"
      :inline="true"
      v-show="showSearch"
      label-width="68px"
    >
      <el-form-item label="运营商" prop="operatorId">
        <el-select
          v-model="queryParams.operatorId"
          placeholder="请选择运营商"
          clearable
          @change="handleOperatorChange"
          style="width: 280px"
          popper-class="operator-select-dropdown"
        >
          <el-option
            v-for="operator in operatorOptions"
            :key="operator.id"
            :label="operator.companyName"
            :value="operator.id"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="场库" prop="warehouseId">
        <el-select
          v-model="queryParams.warehouseId"
          placeholder="请选择场库"
          clearable
          style="width: 200px"
          popper-class="warehouse-select-dropdown"
        >
          <el-option
            v-for="warehouse in warehouseOptions"
            :key="warehouse.id"
            :label="warehouse.warehouseName"
            :value="warehouse.id"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="管理人员" prop="managerName">
        <el-input
          v-model="queryParams.managerName"
          placeholder="请输入管理人员姓名"
          clearable
          style="width: 200px"
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="联系电话" prop="managerPhone">
        <el-input
          v-model="queryParams.managerPhone"
          placeholder="请输入管理人员电话"
          clearable
          style="width: 200px"
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery"
          >搜索</el-button
        >
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>
    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="Plus"
          @click="handleAdd"
          v-hasPermi="['platform:warehouseManager:add']"
          >新增</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="Edit"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['platform:warehouseManager:edit']"
          >修改</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="Delete"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['platform:warehouseManager:remove']"
          >删除</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="Download"
          @click="handleExport"
          v-hasPermi="['platform:warehouseManager:export']"
          >导出</el-button
        >
      </el-col>
      <right-toolbar
        v-model:showSearch="showSearch"
        @queryTable="getList"
      ></right-toolbar>
    </el-row>

    <el-table
      v-loading="loading"
      :data="managerList"
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" width="55" align="center" />
      <!-- <el-table-column label="管理员ID" align="center" prop="id" /> -->
      <el-table-column
        label="所属运营商"
        align="center"
        prop="operatorName"
        :show-overflow-tooltip="true"
      />
      <el-table-column
        label="负责场库"
        align="center"
        prop="warehouseName"
        :show-overflow-tooltip="true"
      />
      <el-table-column label="管理人员" align="center" prop="managerName" />
      <el-table-column label="联系电话" align="center" prop="managerPhone" />

      <el-table-column
        label="创建时间"
        align="center"
        prop="createTime"
        width="180"
      >
        <template #default="scope">
          <span>{{ parseTime(scope.row.createTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column
        label="操作"
        align="center"
        class-name="small-padding fixed-width"
      >
        <template #default="scope">
          <el-button
            link
            type="primary"
            icon="Edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['platform:warehouseManager:edit']"
            >修改</el-button
          >
          <el-button
            link
            type="primary"
            icon="Delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['platform:warehouseManager:remove']"
            >删除</el-button
          >
        </template>
      </el-table-column>
    </el-table>

    <custom-pagination
      :total="total"
      v-model:current-page="queryParams.pageNum"
      v-model:page-size="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改场库管理人员信息对话框 -->
    <el-dialog
      :title="title"
      v-model="open"
      width="600px"
      append-to-body
      :close-on-click-modal="false"
      :close-on-press-escape="false"
    >
      <el-form
        ref="managerRef"
        :model="form"
        :rules="rules"
        label-width="120px"
      >
        <el-form-item label="所属运营商" prop="operatorId">
          <el-select
            v-model="form.operatorId"
            placeholder="请选择运营商"
            @change="handleFormOperatorChange"
            style="width: 100%"
            popper-class="operator-select-dropdown"
          >
            <el-option
              v-for="operator in operatorOptions"
              :key="operator.id"
              :label="operator.companyName"
              :value="operator.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="负责场库" prop="warehouseId">
          <el-select
            v-model="form.warehouseId"
            placeholder="请选择场库"
            style="width: 100%"
            popper-class="warehouse-select-dropdown"
          >
            <el-option
              v-for="warehouse in formWarehouseOptions"
              :key="warehouse.id"
              :label="warehouse.warehouseName"
              :value="warehouse.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="管理人员姓名" prop="managerName">
          <el-input
            v-model="form.managerName"
            placeholder="请输入管理人员姓名"
          />
        </el-form-item>
        <el-form-item label="管理人员电话" prop="managerPhone">
          <el-input
            v-model="form.managerPhone"
            placeholder="请输入管理人员电话"
          />
        </el-form-item>

        <el-form-item label="备注" prop="remark">
          <el-input
            v-model="form.remark"
            type="textarea"
            placeholder="请输入内容"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="WarehouseManager">
import {
  listWarehouseManager,
  getWarehouseManager,
  delWarehouseManager,
  addWarehouseManager,
  updateWarehouseManager,
} from "@/api/platform/warehouseManager";
import { optionSelectOperator } from "@/api/platform/operator";
import {
  optionSelectWarehouse,
  optionSelectWarehouseByOperator,
} from "@/api/platform/warehouse";
import CustomPagination from "@/components/CustomPagination/index.vue";

const { proxy } = getCurrentInstance();

// 注册组件
defineOptions({
  components: {
    CustomPagination,
  },
});

const managerList = ref([]);
const operatorOptions = ref([]);
const warehouseOptions = ref([]);
const formWarehouseOptions = ref([]);
const open = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const title = ref("");

const data = reactive({
  form: {},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    operatorId: null,
    warehouseId: null,
    managerName: null,
    managerPhone: null,
  },
  rules: {
    operatorId: [
      { required: true, message: "所属运营商不能为空", trigger: "change" },
    ],
    warehouseId: [
      { required: true, message: "负责场库不能为空", trigger: "change" },
    ],
    managerName: [
      { required: true, message: "管理人员姓名不能为空", trigger: "blur" },
    ],
    managerPhone: [
      { required: true, message: "管理人员电话不能为空", trigger: "blur" },
      {
        pattern: /^1[3|4|5|6|7|8|9][0-9]\d{8}$/,
        message: "请输入正确的手机号码",
        trigger: "blur",
      },
    ],
  },
});

const { queryParams, form, rules } = toRefs(data);





/** 查询场库管理人员信息列表 */
function getList() {
  loading.value = true;
  listWarehouseManager(queryParams.value).then((response) => {
    managerList.value = response.rows;
    total.value = response.total;
    loading.value = false;
  }).catch((error) => {
    console.error('获取场库管理人员列表失败:', error);
    loading.value = false;
  });
}

/** 查询运营商下拉列表 */
function getOperatorOptions() {
  return optionSelectOperator().then((response) => {
    operatorOptions.value = response.data;
    return response;
  }).catch((error) => {
    console.error('获取运营商选项失败:', error);
    throw error;
  });
}

/** 查询场库下拉列表 */
function getWarehouseOptions() {
  optionSelectWarehouse().then((response) => {
    warehouseOptions.value = response.data;
  });
}

/** 运营商变化时更新场库列表 */
function handleOperatorChange(operatorId) {
  queryParams.value.warehouseId = null;
  if (operatorId) {
    optionSelectWarehouseByOperator(operatorId).then((response) => {
      warehouseOptions.value = response.data;
    });
  } else {
    getWarehouseOptions();
  }
}

/** 表单运营商变化时更新场库列表 */
function handleFormOperatorChange(operatorId) {
  form.value.warehouseId = null;
  if (operatorId) {
    optionSelectWarehouseByOperator(operatorId).then((response) => {
      formWarehouseOptions.value = response.data;
    });
  } else {
    formWarehouseOptions.value = [];
  }
}

// 取消按钮
function cancel() {
  open.value = false;
  reset();
}

// 表单重置
function reset() {
  form.value = {
    id: null,
    operatorId: null,
    warehouseId: null,
    managerName: null,
    managerPhone: null,
    remark: null,
  };
  formWarehouseOptions.value = [];
  proxy.resetForm("managerRef");
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryRef");
  warehouseOptions.value = [];
  getWarehouseOptions();
  handleQuery();
}

// 多选框选中数据
function handleSelectionChange(selection) {
  ids.value = selection.map((item) => item.id);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
}

/** 新增按钮操作 */
function handleAdd() {
  reset();

  // 确保运营商选项数据已加载
  if (operatorOptions.value.length === 0) {
    getOperatorOptions();
  }

  open.value = true;
  title.value = "添加场库管理人员信息";
}

/** 修改按钮操作 */
function handleUpdate(row) {
  reset();
  const _id = row.id || ids.value;

  // 确保运营商选项数据已加载
  const loadOperatorOptions = operatorOptions.value.length === 0
    ? getOperatorOptions()
    : Promise.resolve();

  Promise.resolve(loadOperatorOptions).then(() => {
    return getWarehouseManager(_id);
  }).then((response) => {
    form.value = response.data;

    // 根据运营商ID加载场库列表
    if (form.value.operatorId) {
      return optionSelectWarehouseByOperator(form.value.operatorId).then((res) => {
        formWarehouseOptions.value = res.data;
      });
    } else {
      // 如果没有运营商ID，加载所有场库
      return optionSelectWarehouse().then((res) => {
        formWarehouseOptions.value = res.data;
      });
    }
  }).then(() => {
    open.value = true;
    title.value = "修改场库管理人员信息";
  }).catch((error) => {
    console.error('修改操作失败:', error);
    proxy.$modal.msgError('获取数据失败，请重试');
  });
}

/** 提交按钮 */
function submitForm() {
  proxy.$refs["managerRef"].validate((valid) => {
    if (valid) {
      if (form.value.id != null) {
        updateWarehouseManager(form.value).then((response) => {
          proxy.$modal.msgSuccess("修改成功");
          open.value = false;
          getList();
        });
      } else {
        addWarehouseManager(form.value).then((response) => {
          proxy.$modal.msgSuccess("新增成功");
          open.value = false;
          getList();
        });
      }
    }
  });
}

/** 删除按钮操作 */
function handleDelete(row) {
  const _ids = row.id || ids.value;
  proxy.$modal
    .confirm('是否确认删除场库管理人员信息编号为"' + _ids + '"的数据项？')
    .then(function () {
      return delWarehouseManager(_ids);
    })
    .then(() => {
      getList();
      proxy.$modal.msgSuccess("删除成功");
    })
    .catch(() => {});
}

/** 导出按钮操作 */
function handleExport() {
  proxy.download(
    "system/platform/warehouseManager/export",
    {
      ...queryParams.value,
    },
    `warehouseManager_${new Date().getTime()}.xlsx`
  );
}

getList();
getOperatorOptions();
getWarehouseOptions();
</script>

<style scoped>
/* 表格样式优化 */
.el-table {
  border-radius: 4px;
  overflow: hidden;
}

.el-table th {
  background-color: #fafafa;
  color: #606266;
  font-weight: 500;
}

.el-table td {
  border-bottom: 1px solid #ebeef5;
}

.el-table tr:hover td {
  background-color: #f5f7fa;
}

.el-tag {
  border-radius: 12px;
  font-size: 12px;
  padding: 0 8px;
  height: 24px;
  line-height: 22px;
}

/* 表单样式 */
.el-form-item {
  margin-bottom: 18px;
}

/* 查询表单样式 */
.el-form--inline .el-form-item {
  margin-right: 15px;
  margin-bottom: 15px;
}

/* 按钮样式 */
.el-button--text {
  padding: 0;
  margin-right: 8px;
}

.el-button--text:last-child {
  margin-right: 0;
}


</style>

<style>
/* 运营商下拉框样式 */
.operator-select-dropdown {
  min-width: 300px !important;
}

.operator-select-dropdown .el-select-dropdown__item {
  white-space: nowrap !important;
  overflow: hidden !important;
  text-overflow: ellipsis !important;
  padding: 0 20px !important;
  line-height: 34px !important;
  height: 34px !important;
}

/* 场库下拉框样式 */
.warehouse-select-dropdown {
  min-width: 250px !important;
}

.warehouse-select-dropdown .el-select-dropdown__item {
  white-space: nowrap !important;
  overflow: hidden !important;
  text-overflow: ellipsis !important;
  padding: 0 20px !important;
  line-height: 34px !important;
  height: 34px !important;
}
</style>
