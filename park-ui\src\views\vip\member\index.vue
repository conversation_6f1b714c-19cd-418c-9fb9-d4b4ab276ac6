<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="手机号码" prop="phoneNumber">
        <el-input v-model="queryParams.phoneNumber" placeholder="请输入手机号码" clearable style="width: 200px"
          @keyup.enter="handleQuery" />
      </el-form-item>
      <el-form-item label="车牌号" prop="plateNo">
        <el-input v-model="queryParams.plateNo" placeholder="请输入车牌号" clearable style="width: 200px"
          @keyup.enter="handleQuery" />
      </el-form-item>

      <el-form-item label="场库" prop="warehouseId">
        <el-cascader
          v-model="queryParams.warehouseId"
          :options="warehouseCascaderOptions"
          :props="{
            value: 'value',
            label: 'label',
            children: 'children',
            emitPath: false,
            checkStrictly: true,
            expandTrigger: 'hover'
          }"
          placeholder="请选择场库或停车场"
          style="width: 200px"
          clearable
          filterable
          :show-all-levels="false"
        />
      </el-form-item>

      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="primary" plain icon="Plus" @click="handleAdd" v-hasPermi="['vip:member:add']">新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="success" plain icon="Edit" :disabled="single" @click="handleUpdate"
          v-hasPermi="['vip:member:edit']">修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="danger" plain icon="Delete" :disabled="multiple" @click="handleDelete"
          v-hasPermi="['vip:member:remove']">删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="warning" plain icon="Download" @click="handleExport"
          v-hasPermi="['vip:member:export']">导出</el-button>
      </el-col>
      <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="memberList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="手机号码" align="center" prop="phoneNumber">
        <template #default="scope">
          <el-tag type="info" effect="plain" size="small">
            <i class="el-icon-phone" style="margin-right: 4px;"></i>
            {{ scope.row.phoneNumber }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="车牌号" align="center" prop="plateNo">
        <template #default="scope">
          <el-tag
            :type="getPlateNoTagType(scope.row.plateNo)"
            :color="getPlateNoColor(scope.row.plateNo)"
            effect="plain"
          >
            {{ scope.row.plateNo }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="场库" align="center">
        <template #default="scope">
          <span>{{ scope.row.warehouseName }}</span>
        </template>
      </el-table-column>
      <!-- <el-table-column label="套餐名称" align="center" prop="packageName" width="140">
        <template #default="scope">
          <el-tag v-if="scope.row.packageName" type="warning" effect="light" size="small">
            <i class="el-icon-box" style="margin-right: 4px;"></i>
            {{ scope.row.packageName }}
          </el-tag>
          <span v-else style="color: #909399;">未绑定套餐</span>
        </template>
      </el-table-column> -->
      <el-table-column label="会员类型" align="center" prop="vipType" width="100">
        <template #default="scope">
          <dict-tag
            :options="vip_member_type"
            :value="scope.row.vipType"
          />
        </template>
      </el-table-column>
      <el-table-column label="VIP开始时间" align="center" prop="beginVipTime" width="180">
        <template #default="scope">
          <div style="display: flex; align-items: center; justify-content: center;">
            <i class="el-icon-time" style="color: #67C23A; margin-right: 4px;"></i>
            <span style="color: #606266;">{{ parseTime(scope.row.beginVipTime) }}</span>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="VIP结束时间" align="center" prop="endVipTime" width="180">
        <template #default="scope">
          <div style="display: flex; align-items: center; justify-content: center;">
            <i class="el-icon-time" style="color: #F56C6C; margin-right: 4px;"></i>
            <span :style="getVipEndTimeStyle(scope.row.endVipTime)">{{ parseTime(scope.row.endVipTime) }}</span>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="创建时间" align="center" prop="createTime" width="180">
        <template #default="scope">
          <span>{{ parseTime(scope.row.createTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template #default="scope">
          <el-button link type="primary" icon="View" @click="handleView(scope.row)">查看</el-button>
          <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)"
            v-hasPermi="['vip:member:edit']">修改</el-button>
          <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)"
            v-hasPermi="['vip:member:remove']">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <custom-pagination :total="total" v-model:current-page="queryParams.pageNum"
      v-model:page-size="queryParams.pageSize" @pagination="getList" />

    <!-- 添加或修改会员信息对话框 -->
    <el-dialog
      :title="title"
      v-model="open"
      width="600px"
      append-to-body
      :close-on-click-modal="false"
      :close-on-press-escape="false"
    >
      <el-form ref="memberRef" :model="form" :rules="rules" label-width="100px">
        <!-- 场库选择区域 -->
        <el-row>
          <el-col :span="24">
            <el-form-item label="场库/停车场" prop="warehouseId">
              <el-cascader
                ref="cascaderRef"
                v-model="form.warehouseId"
                :options="warehouseCascaderOptions"
                :props="{
                  value: 'value',
                  label: 'label',
                  children: 'children',
                  emitPath: false,
                  checkStrictly: true,
                  expandTrigger: 'hover'
                }"
                placeholder="请选择场库或停车场"
                style="width: 100%"
                clearable
                filterable
                :show-all-levels="false"
                @change="handleFormWarehouseChange"
              />
            </el-form-item>
          </el-col>
        </el-row>


        <!-- 分隔线 -->
        <el-divider content-position="left">会员基本信息</el-divider>

        <!-- 会员ID显示（仅修改时显示） -->
        <el-row v-if="form.id">
          <el-col :span="24">
            <el-form-item label="会员ID">
              <el-input v-model="form.id" disabled style="background-color: #f5f7fa;" />
            </el-form-item>
          </el-col>
        </el-row>

        <!-- 会员基本信息区域 -->
        <el-row>
          <el-col :span="12">
            <el-form-item label="手机号码" prop="phoneNumber">
              <el-input v-model="form.phoneNumber" placeholder="请输入手机号码" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="车牌号" prop="plateNo">
              <el-input v-model="form.plateNo" placeholder="请输入车牌号" />
            </el-form-item>
          </el-col>
        </el-row>


        <!-- 套餐选择区域 -->
        <el-divider content-position="left">套餐选择</el-divider>
        <el-row>
          <el-col :span="12">
            <el-form-item label="套餐选择" prop="packageId">
              <el-select v-model="form.packageId" placeholder="请选择套餐" clearable filterable
                @change="handlePackageChange">
                <el-option v-for="pkg in packageOptions" :key="pkg.id" :label="pkg.packageName" :value="pkg.id" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="会员类型" prop="vipType">
              <el-select v-model="form.vipType" placeholder="请选择会员类型" clearable>
                <el-option
                  v-for="dict in vip_member_type"
                  :key="dict.value"
                  :label="dict.label"
                  :value="parseInt(dict.value)"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <!-- VIP时间设置区域 -->
        <el-divider content-position="left">VIP时间设置</el-divider>
        <el-row>
          <el-col :span="12">
            <el-form-item label="VIP开始时间" prop="beginVipTime">
              <el-date-picker v-model="form.beginVipTime" type="datetime" placeholder="选择VIP开始时间"
                format="YYYY-MM-DD HH:mm:ss" value-format="YYYY-MM-DD HH:mm:ss" style="width: 100%" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="VIP结束时间" prop="endVipTime">
              <el-date-picker v-model="form.endVipTime" type="datetime" placeholder="选择VIP结束时间"
                format="YYYY-MM-DD HH:mm:ss" value-format="YYYY-MM-DD HH:mm:ss" style="width: 100%" />
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item label="备注" prop="remark">
          <el-input v-model="form.remark" type="textarea" placeholder="请输入备注" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 查看会员详情对话框 -->
    <el-dialog
      title="会员详情"
      v-model="viewOpen"
      width="600px"
      append-to-body
      :close-on-click-modal="false"
      :close-on-press-escape="false"
    >
      <el-descriptions :column="2" border>
        <el-descriptions-item label="会员ID" :span="2">
          <el-tag type="info" size="large">{{ viewData?.id || '-' }}</el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="手机号码">
          {{ viewData?.phoneNumber || '-' }}
        </el-descriptions-item>
        <el-descriptions-item label="车牌号">
          <el-tag
            v-if="viewData?.plateNo"
            :type="getPlateNoTagType(viewData.plateNo)"
            :color="getPlateNoColor(viewData.plateNo)"
            effect="plain"
          >
            {{ viewData.plateNo }}
          </el-tag>
          <span v-else style="color: #909399;">-</span>
        </el-descriptions-item>
        <el-descriptions-item label="套餐名称">
          {{ viewData?.packageName || '-' }}
        </el-descriptions-item>
        <el-descriptions-item label="会员类型">
          <dict-tag
            :options="vip_member_type"
            :value="viewData?.vipType"
          />
        </el-descriptions-item>
        <el-descriptions-item label="运营商" :span="2">
          {{ viewData?.operatorName || '-' }}
        </el-descriptions-item>
        <el-descriptions-item label="场库">
          {{ viewData?.warehouseName || '-' }}
        </el-descriptions-item>
        <el-descriptions-item label="VIP开始时间">
          {{ parseTime(viewData?.beginVipTime) }}
        </el-descriptions-item>
        <el-descriptions-item label="VIP结束时间" :span="2">
          {{ parseTime(viewData?.endVipTime) }}
        </el-descriptions-item>
        <el-descriptions-item label="创建时间" :span="2">
          {{ parseTime(viewData?.createTime) }}
        </el-descriptions-item>
        <el-descriptions-item label="备注" :span="2" v-if="viewData?.remark">
          {{ viewData.remark }}
        </el-descriptions-item>
      </el-descriptions>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="viewOpen = false">关 闭</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="VipMember">
import { nextTick } from "vue";
import {
  listVipMember,
  getVipMember,
  delVipMember,
  addVipMember,
  updateVipMember,
  getWarehouseOptions,
} from "@/api/vip/member";
import { getPackagesByWarehouse } from "@/api/vip/package";
import CustomPagination from "@/components/CustomPagination/index.vue";

// 注册组件
defineOptions({
  components: {
    CustomPagination,
  },
});

const { proxy } = getCurrentInstance();
const { vip_member_type } = proxy.useDict("vip_member_type");


const memberList = ref([]);
const open = ref(false);
const viewOpen = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const title = ref("");
const viewData = ref({});

// 选项数据
const warehouseOptions = ref([]);
const warehouseCascaderOptions = ref([]);
const packageOptions = ref([]);

const data = reactive({
  form: {},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    phoneNumber: null,
    plateNo: null,
    warehouseId: null,
  },
  rules: {
    plateNo: [
      { required: true, message: "车牌号不能为空", trigger: "blur" },
    ],
    packageId: [
      { required: true, message: "请选择套餐", trigger: "change" },
    ],
    beginVipTime: [
      { required: true, message: "VIP开始时间不能为空", trigger: "change" },
    ],
    endVipTime: [
      { required: true, message: "VIP结束时间不能为空", trigger: "change" },
    ],
  },
});

const { queryParams, form, rules } = toRefs(data);

/** 获取等级标签类型 */
function getLevelTagType(level) {
  const levelMap = {
    1: "primary",
    2: "success",
    3: "warning",
    4: "danger",
    5: "info",
  };
  return levelMap[level] || "primary";
}

/** 获取等级文字 */
function getLevelText(level) {
  const levelMap = {
    1: "普通",
    2: "银卡",
    3: "金卡",
    4: "白金",
    5: "钻石",
  };
  return levelMap[level] || "未知";
}

/** 获取状态标签类型 */
function getStatusTagType(status) {
  const statusMap = {
    1: "success",
    2: "warning",
    3: "danger",
    4: "info",
  };
  return statusMap[status] || "info";
}

/** 获取状态文字 */
function getStatusText(status) {
  const statusMap = {
    1: "正常",
    2: "冻结",
    3: "黑名单",
    4: "已注销",
  };
  return statusMap[status] || "未知";
}

// 获取车牌号标签类型
function getPlateNoTagType(plateNo) {
  if (!plateNo) return 'info';
  // 8位为新能源车牌，7位为普通车牌
  return plateNo.length === 8 ? 'success' : 'primary';
}

// 获取车牌号颜色
function getPlateNoColor(plateNo) {
  if (!plateNo) return '#909399';
  // 新能源车牌：浅绿色，普通车牌：浅蓝色
  return plateNo.length === 8 ? '#d4edda' : '#cce7ff';
}

// 获取VIP结束时间样式
function getVipEndTimeStyle(endTime) {
  if (!endTime) return { color: '#909399' };

  const now = new Date();
  const end = new Date(endTime);
  const diffDays = Math.ceil((end - now) / (1000 * 60 * 60 * 24));

  if (diffDays < 0) {
    return { color: '#F56C6C', fontWeight: 'bold' }; // 已过期：红色加粗
  } else if (diffDays <= 7) {
    return { color: '#E6A23C', fontWeight: 'bold' }; // 7天内到期：橙色加粗
  } else if (diffDays <= 30) {
    return { color: '#E6A23C' }; // 30天内到期：橙色
  } else {
    return { color: '#67C23A' }; // 正常：绿色
  }
}

/** 查询会员信息列表 */
function getList() {
  loading.value = true;
  listVipMember(queryParams.value).then((response) => {
    memberList.value = response.rows;
    total.value = response.total;
    loading.value = false;
  });
}



/** 获取场库选项 */
function loadWarehouseOptions() {
  getWarehouseOptions().then((response) => {
    warehouseOptions.value = response.data;
    // 同时构建层级选项（用于cascader）
    warehouseCascaderOptions.value = buildWarehouseCascaderOptions(response.data);
  });
}



/** 构建级联选择器选项 */
function buildWarehouseCascaderOptions(warehouses) {
  if (!warehouses || warehouses.length === 0) {
    return [];
  }

  // 分离主场库和子场库
  const mainWarehouses = warehouses.filter(w => w.parentId === "0");
  const subWarehouses = warehouses.filter(w => w.parentId !== "0");

  // 构建级联结构
  return mainWarehouses.map(mainWarehouse => {
    const children = subWarehouses
      .filter(sub => sub.parentId === mainWarehouse.id)
      .map(sub => ({
        value: sub.id,
        label: sub.warehouseName,
        isLeaf: true
      }));

    return {
      value: mainWarehouse.id,
      label: mainWarehouse.warehouseName,
      children: children.length > 0 ? children : undefined
    };
  });
}

/** 判断是否为主场库 */
function isMainWarehouse(warehouse) {
  return warehouse && warehouse.parentId === "0";
}

/** 根据目标ID构建完整的场库路径 */
function buildWarehousePath(targetId, warehouses) {
  if (!targetId || !warehouses) {
    return null;
  }

  // 转换为字符串进行比较
  targetId = String(targetId);

  // 查找当前场库
  const currentWarehouse = warehouses.find(w => String(w.id) === targetId);
  if (!currentWarehouse) {
    return null;
  }

  // 如果是主场库，直接返回ID
  if (isMainWarehouse(currentWarehouse)) {
    return [currentWarehouse.id];
  }

  // 如果是子场库，需要找到父场库
  const parentWarehouse = warehouses.find(w => String(w.id) === String(currentWarehouse.parentId));
  if (parentWarehouse) {
    return [parentWarehouse.id, currentWarehouse.id];
  }

  // 如果找不到父场库，返回当前场库ID
  return [currentWarehouse.id];
}

/** 表单-场库变化处理 */
function handleFormWarehouseChange(warehouseId) {
  // 重置套餐选择
  form.value.packageId = null;
  packageOptions.value = [];

  if (warehouseId) {
    // 加载该场库的套餐选项
    getPackagesByWarehouse(warehouseId).then((response) => {
      packageOptions.value = response.data || [];
    }).catch(() => {
      packageOptions.value = [];
    });
  }
}

/** 套餐变化处理 */
function handlePackageChange(packageId) {
  // 可以在这里根据套餐信息自动设置VIP时间等
  if (packageId) {
    const selectedPackage = packageOptions.value.find(pkg => pkg.id === packageId);
    if (selectedPackage) {
      // 这里可以根据套餐类型自动计算结束时间
    }
  }
}

// 取消按钮
function cancel() {
  open.value = false;
  reset();
}

// 表单重置
function reset() {
  form.value = {
    id: null,
    warehouseId: null,
    userId: null,
    phoneNumber: null,
    plateNo: null,
    beginVipTime: null,
    endVipTime: null,
    packageId: null,
    dlySystemId: null,
    remark: null,
  };
  packageOptions.value = [];
  proxy.resetForm("memberRef");
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryRef");
  // 重置查询参数到初始状态
  Object.assign(queryParams.value, {
    pageNum: 1,
    pageSize: 10,
    phoneNumber: null,
    plateNo: null,
    warehouseId: null
  });
  handleQuery();
}

// 多选框选中数据
function handleSelectionChange(selection) {
  ids.value = selection.map((item) => item.id);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
}

/** 查看按钮操作 */
function handleView(row) {
  const _id = row.id || ids.value;
  getVipMember(_id).then((response) => {
    viewData.value = response.data || {};
    viewOpen.value = true;
  }).catch((error) => {
    console.error('获取会员详情失败:', error);
    proxy.$modal.msgError("获取会员详情失败");
  });
}

/** 新增按钮操作 */
function handleAdd() {
  reset();

  // 确保场库选项已加载
  if (warehouseCascaderOptions.value.length === 0) {
    loadWarehouseOptions();
  }

  open.value = true;
  title.value = "添加会员信息";
}

/** 修改按钮操作 */
function handleUpdate(row) {
  reset();
  const _id = row.id || ids.value;

  // 先确保场库选项已加载，然后获取会员数据
  Promise.all([
    getWarehouseOptions(),
    getVipMember(_id)
  ]).then(([warehouseResponse, memberResponse]) => {
    // 构建级联选择器选项
    warehouseOptions.value = warehouseResponse.data;
    warehouseCascaderOptions.value = buildWarehouseCascaderOptions(warehouseResponse.data);

    // 先打开对话框
    open.value = true;
    title.value = "修改会员信息";

    // 使用 nextTick 确保级联选择器组件已渲染，然后设置表单数据
    nextTick(() => {
      form.value = memberResponse.data;

      // 再次使用 nextTick 确保数据绑定完成，然后处理级联选择器回显
      nextTick(() => {
        // 根据 warehouseId 构建完整的路径数组来实现回显
        if (form.value.warehouseId) {
          const warehousePath = buildWarehousePath(form.value.warehouseId, warehouseResponse.data);

          if (warehousePath && warehousePath.length > 0) {
            // 设置完整的路径数组来实现回显
            form.value.warehouseId = warehousePath;
          }
        }

        // 加载该场库的套餐选项
        if (form.value.warehouseId) {
          const actualWarehouseId = Array.isArray(form.value.warehouseId)
            ? form.value.warehouseId[form.value.warehouseId.length - 1]
            : form.value.warehouseId;

          getPackagesByWarehouse(actualWarehouseId).then((packageResponse) => {
            packageOptions.value = packageResponse.data || [];
          }).catch(() => {
            packageOptions.value = [];
          });
        }
      });
    });
  });
}

/** 提交按钮 */
function submitForm() {
  proxy.$refs["memberRef"].validate((valid) => {
    if (valid) {
      // 验证必须选择场库
      if (!form.value.warehouseId) {
        proxy.$modal.msgError("请选择场库");
        return;
      }

      // 处理级联选择器的值
      let warehouseId = form.value.warehouseId;
      if (Array.isArray(warehouseId)) {
        // 如果是数组，取最后一个值（子场库ID或主场库ID）
        warehouseId = warehouseId[warehouseId.length - 1];
      }
      form.value.warehouseId = warehouseId;

      if (form.value.id != null) {
        updateVipMember(form.value).then((response) => {
          proxy.$modal.msgSuccess("修改成功");
          open.value = false;
          getList();
        });
      } else {
        addVipMember(form.value).then((response) => {
          proxy.$modal.msgSuccess("新增成功");
          open.value = false;
          getList();
        });
      }
    }
  });
}

/** 删除按钮操作 */
function handleDelete(row) {
  const _ids = row.id || ids.value;
  proxy.$modal
    .confirm('是否确认删除会员信息编号为"' + _ids + '"的数据项？')
    .then(function () {
      return delVipMember(_ids);
    })
    .then(() => {
      getList();
      proxy.$modal.msgSuccess("删除成功");
    })
    .catch(() => { });
}

/** 导出按钮操作 */
function handleExport() {
  proxy.download(
    "system/vip/member/export",
    {
      ...queryParams.value,
    },
    `vip_member_${new Date().getTime()}.xlsx`
  );
}

getList();
loadWarehouseOptions();
</script>

<style scoped>
/* 表格样式优化 */
.el-table {
  border-radius: 4px;
  overflow: hidden;
}

.el-table th {
  background-color: #fafafa;
  color: #606266;
  font-weight: 500;
}

.el-table td {
  border-bottom: 1px solid #ebeef5;
}

.el-table tr:hover td {
  background-color: #f5f7fa;
}

.el-tag {
  border-radius: 12px;
  font-size: 12px;
  padding: 0 8px;
  height: 24px;
  line-height: 22px;
}

/* 会员详情样式 */
.el-descriptions {
  margin-top: 20px;
}

.el-descriptions__label {
  font-weight: 500;
  color: #606266;
  background-color: #fafafa;
}

.el-descriptions__content {
  color: #303133;
}

/* 表单样式 */
.el-form-item {
  margin-bottom: 18px;
}

.el-divider {
  margin: 24px 0;
}

.el-divider__text {
  font-weight: 600;
  color: #303133;
}

/* 查询表单样式 */
.el-form--inline .el-form-item {
  margin-right: 15px;
  margin-bottom: 15px;
}

/* 按钮样式 */
.el-button--text {
  padding: 0;
  margin-right: 8px;
}

.el-button--text:last-child {
  margin-right: 0;
}
</style>
