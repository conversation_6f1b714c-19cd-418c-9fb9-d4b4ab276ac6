version: '3.8'

services:
  # MySQL数据库
  mysql:
    image: mysql:8.0.35
    container_name: ${MYSQL_HOST}
    restart: always
    environment:
      MYSQL_ROOT_PASSWORD: ${MYSQL_ROOT_PASSWORD}
      MYSQL_DATABASE: ${MYSQL_DATABASE}
      MYSQL_USER: ${MYSQL_USER}
      MYSQL_PASSWORD: ${MYSQL_PASSWORD}
      TZ: ${TZ}
    ports:
      - "${MYSQL_PORT}:3306"
    volumes:
      - mysql_data:/var/lib/mysql
      - mysql_logs:/var/log/mysql
      - ./sql:/docker-entrypoint-initdb.d  # 启用自动初始化数据库
    command: --default-authentication-plugin=mysql_native_password
    healthcheck:
      test: ["CMD", "mysqladmin", "ping", "-h", "localhost", "-u", "root", "-p${MYSQL_ROOT_PASSWORD}"]
      timeout: 20s
      retries: 10
      interval: 10s
    networks:
      - park-network

  # Redis缓存
  redis:
    image: redis:7-alpine
    container_name: ${REDIS_HOST}
    restart: always
    ports:
      - "6380:${REDIS_PORT}"
    volumes:
      - redis_data:/data
      - redis_logs:/var/log/redis
    command: redis-server --appendonly yes --requirepass ${REDIS_PASSWORD}
    healthcheck:
      test: ["CMD", "redis-cli", "-a", "${REDIS_PASSWORD}", "ping"]
      timeout: ${HEALTH_CHECK_TIMEOUT}
      retries: ${HEALTH_CHECK_RETRIES}
      interval: 10s
    networks:
      - park-network

  # Nacos注册中心
  nacos:
    image: nacos/nacos-server:v2.2.3
    container_name: ${NACOS_HOST}
    restart: always
    environment:
      MODE: standalone
      SPRING_DATASOURCE_PLATFORM: mysql
      MYSQL_SERVICE_HOST: ${MYSQL_HOST}
      MYSQL_SERVICE_PORT: ${MYSQL_PORT}
      MYSQL_SERVICE_DB_NAME: nacos
      MYSQL_SERVICE_USER: root
      MYSQL_SERVICE_PASSWORD: ${MYSQL_ROOT_PASSWORD}
      NACOS_AUTH_ENABLE: ${NACOS_AUTH_ENABLE}
      NACOS_AUTH_TOKEN: ${NACOS_AUTH_TOKEN}
      NACOS_AUTH_IDENTITY_KEY: ${NACOS_AUTH_IDENTITY_KEY}
      NACOS_AUTH_IDENTITY_VALUE: ${NACOS_AUTH_IDENTITY_VALUE}
      NACOS_AUTH_SYSTEM_TYPE: ${NACOS_AUTH_SYSTEM_TYPE}
      NACOS_AUTH_CACHE_ENABLE: ${NACOS_AUTH_CACHE_ENABLE}
      TZ: ${TZ}
    ports:
      - "${NACOS_PORT}:8848"
      - "9848:9848"
    volumes:
      - nacos_logs:/home/<USER>/logs
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8848/nacos/v1/console/health/readiness"]
      timeout: ${HEALTH_CHECK_TIMEOUT}
      retries: 10
      interval: 15s
    networks:
      - park-network

  # 认证服务
  park-auth:
    image: park/lgjy-auth:${APP_VERSION}
    container_name: park-auth
    restart: always
    ports:
      - "${AUTH_PORT}:9204"
    environment:
      SPRING_PROFILES_ACTIVE: ${SPRING_PROFILES_ACTIVE}
      SPRING_CLOUD_NACOS_DISCOVERY_SERVER_ADDR: ${NACOS_HOST}:${NACOS_PORT}
      SPRING_CLOUD_NACOS_CONFIG_SERVER_ADDR: ${NACOS_HOST}:${NACOS_PORT}
      SPRING_CLOUD_NACOS_USERNAME: ${NACOS_USERNAME}
      SPRING_CLOUD_NACOS_PASSWORD: ${NACOS_PASSWORD}
      SPRING_CLOUD_NACOS_DISCOVERY_USERNAME: ${NACOS_USERNAME}
      SPRING_CLOUD_NACOS_DISCOVERY_PASSWORD: ${NACOS_PASSWORD}
      SPRING_CLOUD_NACOS_CONFIG_USERNAME: ${NACOS_USERNAME}
      SPRING_CLOUD_NACOS_CONFIG_PASSWORD: ${NACOS_PASSWORD}
      SPRING_CLOUD_NACOS_CONFIG_NAMESPACE: ${NACOS_NAMESPACE}
      TZ: ${TZ}
      # 注意：数据库和Redis配置已迁移到Nacos配置中心，支持动态刷新
    volumes:
      - park_auth_logs:/app/logs
    healthcheck:
      test: ["CMD", "wget", "--quiet", "--tries=1", "--spider", "http://localhost:9204/actuator/health"]
      timeout: ${HEALTH_CHECK_TIMEOUT}
      retries: ${HEALTH_CHECK_RETRIES}
      interval: ${HEALTH_CHECK_INTERVAL}
    networks:
      - park-network

  # 微信认证服务
  park-wx-auth:
    image: park/lgjy-wx-auth:3.6.6
    container_name: park-wx-auth
    restart: always
    ports:
      - "9205:9205"
    environment:
      # 环境标识 - 决定加载哪个Nacos配置环境
      SPRING_PROFILES_ACTIVE: test
      # Nacos连接配置 - 必需的基础连接信息
      SPRING_CLOUD_NACOS_DISCOVERY_SERVER_ADDR: park-nacos:8848
      SPRING_CLOUD_NACOS_CONFIG_SERVER_ADDR: park-nacos:8848
      SPRING_CLOUD_NACOS_USERNAME: nacos
      SPRING_CLOUD_NACOS_PASSWORD: Tp9Vc2JxY6HdM
      SPRING_CLOUD_NACOS_DISCOVERY_USERNAME: nacos
      SPRING_CLOUD_NACOS_DISCOVERY_PASSWORD: Tp9Vc2JxY6HdM
      SPRING_CLOUD_NACOS_CONFIG_USERNAME: nacos
      SPRING_CLOUD_NACOS_CONFIG_PASSWORD: Tp9Vc2JxY6HdM
      SPRING_CLOUD_NACOS_CONFIG_NAMESPACE: ${NACOS_NAMESPACE}
      # 注意：Redis、短信服务和微信API配置已迁移到Nacos配置中心，支持动态刷新
    volumes:
      - park_wx_auth_logs:/app/logs
    healthcheck:
      test: ["CMD", "wget", "--quiet", "--tries=1", "--spider", "http://localhost:9205/actuator/health"]
      timeout: 10s
      retries: 5
      interval: 30s
    networks:
      - park-network

  # 系统管理服务
  park-system:
    image: park/lgjy-modules-system:3.6.6
    container_name: park-system
    restart: always
    ports:
      - "9201:9201"
    environment:
      # 环境标识 - 决定加载哪个Nacos配置环境
      SPRING_PROFILES_ACTIVE: test
      # Nacos连接配置 - 必需的基础连接信息
      SPRING_CLOUD_NACOS_DISCOVERY_SERVER_ADDR: park-nacos:8848
      SPRING_CLOUD_NACOS_CONFIG_SERVER_ADDR: park-nacos:8848
      SPRING_CLOUD_NACOS_USERNAME: nacos
      SPRING_CLOUD_NACOS_PASSWORD: Tp9Vc2JxY6HdM
      SPRING_CLOUD_NACOS_DISCOVERY_USERNAME: nacos
      SPRING_CLOUD_NACOS_DISCOVERY_PASSWORD: Tp9Vc2JxY6HdM
      SPRING_CLOUD_NACOS_CONFIG_USERNAME: nacos
      SPRING_CLOUD_NACOS_CONFIG_PASSWORD: Tp9Vc2JxY6HdM
      SPRING_CLOUD_NACOS_CONFIG_NAMESPACE: ${NACOS_NAMESPACE}
      # 注意：数据库、Redis和MyBatis配置已迁移到Nacos配置中心，支持动态刷新
    depends_on:
      - mysql
      - redis
      - nacos
    volumes:
      - park_system_logs:/app/logs
    healthcheck:
      test: ["CMD", "wget", "--quiet", "--tries=1", "--spider", "http://localhost:9201/actuator/health"]
      timeout: 10s
      retries: 5
      interval: 30s
    networks:
      - park-network

  # 文件服务
  park-file:
    image: park/lgjy-modules-file:3.6.6
    container_name: park-file
    restart: always
    ports:
      - "9202:9202"
    environment:
      # 环境标识 - 决定加载哪个Nacos配置环境
      SPRING_PROFILES_ACTIVE: test
      # Nacos连接配置 - 必需的基础连接信息
      SPRING_CLOUD_NACOS_DISCOVERY_SERVER_ADDR: park-nacos:8848
      SPRING_CLOUD_NACOS_CONFIG_SERVER_ADDR: park-nacos:8848
      SPRING_CLOUD_NACOS_USERNAME: nacos
      SPRING_CLOUD_NACOS_PASSWORD: Tp9Vc2JxY6HdM
      SPRING_CLOUD_NACOS_DISCOVERY_USERNAME: nacos
      SPRING_CLOUD_NACOS_DISCOVERY_PASSWORD: Tp9Vc2JxY6HdM
      SPRING_CLOUD_NACOS_CONFIG_USERNAME: nacos
      SPRING_CLOUD_NACOS_CONFIG_PASSWORD: Tp9Vc2JxY6HdM
      SPRING_CLOUD_NACOS_CONFIG_NAMESPACE: ${NACOS_NAMESPACE}
      # 注意：文件存储配置已迁移到Nacos配置中心，支持动态刷新
    volumes:
      - file_data:/app/upload
      - park_file_logs:/app/logs
    healthcheck:
      test: ["CMD", "wget", "--quiet", "--tries=1", "--spider", "http://localhost:9202/actuator/health"]
      timeout: 10s
      retries: 5
      interval: 30s
    networks:
      - park-network

  # 微信小程序服务
  park-wx:
    image: park/lgjy-modules-wx:3.6.6
    container_name: park-wx
    restart: always
    ports:
      - "9206:9206"  # 修复端口冲突：从9202改为9206
    environment:
      # 环境标识 - 决定加载哪个Nacos配置环境
      SPRING_PROFILES_ACTIVE: test
      # Nacos连接配置 - 必需的基础连接信息
      SPRING_CLOUD_NACOS_DISCOVERY_SERVER_ADDR: park-nacos:8848
      SPRING_CLOUD_NACOS_CONFIG_SERVER_ADDR: park-nacos:8848
      SPRING_CLOUD_NACOS_USERNAME: nacos
      SPRING_CLOUD_NACOS_PASSWORD: Tp9Vc2JxY6HdM
      SPRING_CLOUD_NACOS_DISCOVERY_USERNAME: nacos
      SPRING_CLOUD_NACOS_DISCOVERY_PASSWORD: Tp9Vc2JxY6HdM
      SPRING_CLOUD_NACOS_CONFIG_USERNAME: nacos
      SPRING_CLOUD_NACOS_CONFIG_PASSWORD: Tp9Vc2JxY6HdM
      SPRING_CLOUD_NACOS_CONFIG_NAMESPACE: ${NACOS_NAMESPACE}
      # 注意：数据库、Redis、微信API和银联支付配置已迁移到Nacos配置中心，支持动态刷新
    depends_on:
      - mysql
      - redis
      - nacos
    volumes:
      - park_wx_logs:/app/logs
    healthcheck:
      test: ["CMD", "wget", "--quiet", "--tries=1", "--spider", "http://localhost:9206/actuator/health"]
      timeout: 10s
      retries: 5
      interval: 30s
    networks:
      - park-network

  # 道闸设备服务
  park-gate:
    image: park/lgjy-modules-gate:3.6.6
    container_name: park-gate
    restart: always
    ports:
      - "9203:9203"
    environment:
      # 环境标识 - 决定加载哪个Nacos配置环境
      SPRING_PROFILES_ACTIVE: test
      # Nacos连接配置 - 必需的基础连接信息
      SPRING_CLOUD_NACOS_DISCOVERY_SERVER_ADDR: park-nacos:8848
      SPRING_CLOUD_NACOS_CONFIG_SERVER_ADDR: park-nacos:8848
      SPRING_CLOUD_NACOS_USERNAME: nacos
      SPRING_CLOUD_NACOS_PASSWORD: Tp9Vc2JxY6HdM
      SPRING_CLOUD_NACOS_DISCOVERY_USERNAME: nacos
      SPRING_CLOUD_NACOS_DISCOVERY_PASSWORD: Tp9Vc2JxY6HdM
      SPRING_CLOUD_NACOS_CONFIG_USERNAME: nacos
      SPRING_CLOUD_NACOS_CONFIG_PASSWORD: Tp9Vc2JxY6HdM
      SPRING_CLOUD_NACOS_CONFIG_NAMESPACE: ${NACOS_NAMESPACE}
      # 数据库和Redis配置通过环境变量传递给Nacos配置
      DB_HOST: park-mysql
      DB_PORT: 3306
      DB_NAME: parknew
      DB_USERNAME: admin
      DB_PASSWORD: K7PmdL9Rf2Q
      REDIS_HOST: park-redis
      REDIS_PORT: 6379
      REDIS_PASSWORD: qR6bW9kFzT3Zv
      REDIS_DATABASE: 2
      # 注意：数据库和Redis配置已迁移到Nacos配置中心，支持动态刷新
    depends_on:
      - mysql
      - redis
      - nacos
    volumes:
      - park_gate_logs:/app/logs
    healthcheck:
      test: ["CMD", "wget", "--quiet", "--tries=1", "--spider", "http://localhost:9203/actuator/health"]
      timeout: 10s
      retries: 5
      interval: 30s
    networks:
      - park-network

  # 网关服务
  park-gateway:
    image: park/lgjy-gateway:${APP_VERSION}
    container_name: park-gateway
    restart: always
    ports:
      - "${GATEWAY_PORT}:8080"
    environment:
      SPRING_PROFILES_ACTIVE: ${SPRING_PROFILES_ACTIVE}
      SPRING_CLOUD_NACOS_DISCOVERY_SERVER_ADDR: ${NACOS_HOST}:${NACOS_PORT}
      SPRING_CLOUD_NACOS_CONFIG_SERVER_ADDR: ${NACOS_HOST}:${NACOS_PORT}
      SPRING_CLOUD_NACOS_USERNAME: ${NACOS_USERNAME}
      SPRING_CLOUD_NACOS_PASSWORD: ${NACOS_PASSWORD}
      SPRING_CLOUD_NACOS_DISCOVERY_USERNAME: ${NACOS_USERNAME}
      SPRING_CLOUD_NACOS_DISCOVERY_PASSWORD: ${NACOS_PASSWORD}
      SPRING_CLOUD_NACOS_CONFIG_USERNAME: ${NACOS_USERNAME}
      SPRING_CLOUD_NACOS_CONFIG_PASSWORD: ${NACOS_PASSWORD}
      SPRING_CLOUD_NACOS_CONFIG_NAMESPACE: ${NACOS_NAMESPACE}
      TZ: ${TZ}
      # 注意：Redis配置已迁移到Nacos配置中心，支持动态刷新
    depends_on:
      - mysql
      - redis
      - nacos
    volumes:
      - park_gateway_logs:/app/logs
    healthcheck:
      test: ["CMD", "wget", "--quiet", "--tries=1", "--spider", "http://localhost:8080/actuator/health"]
      timeout: 10s
      retries: 5
      interval: 30s
    networks:
      - park-network

  # Jenkins CI/CD服务
  jenkins-park:
    image: jenkins/jenkins:lts
    container_name: jenkins-park
    restart: always
    user: root
    ports:
      - "9001:8080"      # Jenkins Web端口（内部访问）
      - "50001:50000"    # Jenkins Agent端口（避免与现有50000冲突）
    volumes:
      - jenkins_data:/var/jenkins_home
      - jenkins_logs:/var/log/jenkins
      - /var/run/docker.sock:/var/run/docker.sock
      - /usr/bin/docker:/usr/bin/docker
      # 挂载项目代码目录（只读）- 可根据实际部署路径调整
      # - /opt/parking/park-api:/var/jenkins_home/workspace/park-api:ro
      # - /opt/parking/park-ui:/var/jenkins_home/workspace/park-ui:ro
      # - /opt/parking/park-uniapp:/var/jenkins_home/workspace/park-uniapp:ro
    environment:
      - JENKINS_OPTS=--httpPort=8080 --prefix=/jenkins
      - JAVA_OPTS=-Xmx2g -Xms1g
      - TZ=Asia/Shanghai
    networks:
      - park-network

  # 日志清理服务 - 定期清理微服务日志
  log-cleaner:
    image: alpine:latest
    container_name: park-log-cleaner
    restart: "no"  # 手动启动或定时启动
    volumes:
      - park_auth_logs:/logs/park-auth
      - park_wx_auth_logs:/logs/park-wx-auth
      - park_system_logs:/logs/park-system
      - park_file_logs:/logs/park-file
      - park_wx_logs:/logs/park-wx
      - park_gate_logs:/logs/park-gate
      - park_gateway_logs:/logs/park-gateway
      - mysql_logs:/logs/mysql
      - redis_logs:/logs/redis
      - nacos_logs:/logs/nacos
      - jenkins_logs:/logs/jenkins
    command: >
      sh -c "
        echo '开始清理微服务日志...'
        find /logs -name '*.log' -type f -mtime +7 -delete
        find /logs -name '*.log.*' -type f -mtime +3 -delete
        find /logs -name '*.out' -type f -mtime +7 -delete
        find /logs -name '*.err' -type f -mtime +7 -delete
        find /logs -type f -size +100M -delete
        echo '日志清理完成！'
      "
    networks:
      - park-network

volumes:
  mysql_data:
  mysql_logs:
  redis_data:
  redis_logs:
  nacos_logs:
  file_data:
  park_auth_logs:
  park_wx_auth_logs:
  park_system_logs:
  park_file_logs:
  park_wx_logs:
  park_gate_logs:
  park_gateway_logs:
  jenkins_data:
  jenkins_logs:

networks:
  park-network:
    driver: bridge