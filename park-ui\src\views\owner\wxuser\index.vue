<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="用户账号" prop="userName">
        <el-input
          v-model="queryParams.userName"
          placeholder="请输入用户账号"
          clearable
          style="width: 200px"
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="用户昵称" prop="nickName">
        <el-input
          v-model="queryParams.nickName"
          placeholder="请输入用户昵称"
          clearable
          style="width: 200px"
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="手机号" prop="phoneNumber">
        <el-input
          v-model="queryParams.phoneNumber"
          placeholder="请输入手机号"
          clearable
          style="width: 200px"
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <!-- <el-form-item label="状态" prop="status">
        <el-select v-model="queryParams.status" placeholder="用户状态" clearable style="width: 200px">
          <el-option
            v-for="dict in wx_user_status"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item> -->
      <el-form-item label="用户类型" prop="userType">
        <el-select v-model="queryParams.userType" placeholder="用户类型" clearable style="width: 200px">
          <el-option
            v-for="dict in wx_user_type"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>

      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">

      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="Delete"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['owner:wxuser:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="Download"
          @click="handleExport"
          v-hasPermi="['owner:wxuser:export']"
        >导出</el-button>
      </el-col>
      <!-- <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="Check"
          :disabled="multiple"
          @click="handleBatchEnable"
          v-hasPermi="['owner:wxuser:edit']"
        >批量启用</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="info"
          plain
          icon="Close"
          :disabled="multiple"
          @click="handleBatchDisable"
          v-hasPermi="['owner:wxuser:edit']"
        >批量停用</el-button>
      </el-col> -->
      <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="wxUserList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="头像" align="center" prop="img">
        <template #default="scope">
          <el-image
            v-if="scope.row.img"
            style="width: 40px; height: 40px; border-radius: 50%"
            :src="scope.row.img"
            :preview-src-list="[scope.row.img]"
            fit="cover"
          />
          <span v-else>-</span>
        </template>
      </el-table-column>
      <el-table-column label="用户昵称" align="center" prop="nickName" />
      <el-table-column label="用户账号" align="center" prop="userName" />
      <el-table-column label="手机号" align="center" prop="phoneNumber" />
      <!-- <el-table-column label="状态" align="center" prop="status">
        <template #default="scope">
          <dict-tag :options="wx_user_status" :value="scope.row.status"/>
        </template>
      </el-table-column> -->
      <el-table-column label="用户类型" align="center" prop="userType">
        <template #default="scope">
          <dict-tag :options="wx_user_type" :value="scope.row.userType"/>
        </template>
      </el-table-column>

      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template #default="scope">
          <el-button
            link
            type="primary"
            icon="View"
            @click="handleViewCars(scope.row)"
            v-hasPermi="['owner:wxuser:car']"
          >查询车辆</el-button>
          <!-- <el-button
            v-if="scope.row.status === 1"
            link
            type="success"
            icon="Check"
            @click="handleStatusChange(scope.row, 0)"
            v-hasPermi="['owner:wxuser:edit']"
          >启用</el-button>
          <el-button
            v-if="scope.row.status === 0"
            link
            type="danger"
            icon="Close"
            @click="handleStatusChange(scope.row, 1)"
            v-hasPermi="['owner:wxuser:edit']"
          >停用</el-button> -->
          <el-button
            link
            type="danger"
            icon="Delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['owner:wxuser:remove']"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <custom-pagination
      :total="total"
      v-model:current-page="queryParams.pageNum"
      v-model:page-size="queryParams.pageSize"
      @pagination="getList"
    />


    <!-- 查询车辆对话框 -->
    <el-dialog title="用户车辆信息" v-model="carOpen" width="800px" append-to-body>
      <el-table :data="carList" style="width: 100%">
        <el-table-column label="车牌号" align="center" prop="plateNo" />
        <el-table-column label="品牌" align="center" prop="carBrand" />
        <el-table-column label="车型" align="center" prop="carType" />
        <el-table-column label="能源类型" align="center" prop="energyType">
          <template #default="scope">
            <dict-tag :options="energy_type_options" :value="scope.row.energyType"/>
          </template>
        </el-table-column>
        <el-table-column label="是否默认" align="center" prop="isDefault">
          <template #default="scope">
            <dict-tag :options="is_default_options" :value="scope.row.isDefault"/>
          </template>
        </el-table-column>
        <el-table-column label="创建时间" align="center" prop="createTime" width="180">
          <template #default="scope">
            <span>{{ parseTime(scope.row.createTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
          </template>
        </el-table-column>
      </el-table>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="carOpen = false">关 闭</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="WxUser">
import { listWxUser, delWxUser, exportWxUser, batchEnableWxUser, batchDisableWxUser, changeWxUserStatus, getUserCars } from "@/api/owner/wxuser";
import CustomPagination from "@/components/CustomPagination/index.vue";

const { proxy } = getCurrentInstance();
const { wx_user_status, wx_user_type } = proxy.useDict('wx_user_status', 'wx_user_type');

// 能源类型和默认车辆选项（静态定义）
const energy_type_options = ref([
  { label: '燃油', value: '1' },
  { label: '纯电', value: '2' },
  { label: '混动', value: '3' }
]);

const is_default_options = ref([
  { label: '否', value: '0' },
  { label: '是', value: '1' }
]);

const wxUserList = ref([]);
const carList = ref([]);
const carOpen = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);

const data = reactive({
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    userName: null,
    nickName: null,
    phoneNumber: null,
    status: null,
    userType: null
  }
});

const { queryParams } = toRefs(data);

/** 查询小程序用户列表 */
function getList() {
  loading.value = true;
  listWxUser(queryParams.value).then(response => {
    wxUserList.value = response.rows;
    total.value = response.total;
    loading.value = false;
  });
}

/** 用户状态修改 */
function handleStatusChange(row, newStatus) {
  let text = newStatus === 0 ? "启用" : "停用";
  proxy.$modal
    .confirm('确认要"' + text + '""' + row.userName + '"用户吗?')
    .then(function () {
      // 调用单个用户状态修改API
      console.log('调用状态修改API:', { id: row.id, status: newStatus });
      return changeWxUserStatus(row.id, newStatus);
    })
    .then((response) => {
      console.log('状态修改响应:', response);
      proxy.$modal.msgSuccess(text + "成功");
      getList();
    })
    .catch(function (error) {
      console.error('状态修改失败:', error);
      if (error.response && error.response.data && error.response.data.msg) {
        proxy.$modal.msgError("操作失败：" + error.response.data.msg);
      } else {
        proxy.$modal.msgError("操作失败，请稍后重试");
      }
    });
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryRef");
  // 重置查询参数到初始状态
  Object.assign(queryParams.value, {
    pageNum: 1,
    pageSize: 10,
    userName: null,
    nickName: null,
    phoneNumber: null,
    status: null,
    userType: null
  });
  handleQuery();
}

// 多选框选中数据
function handleSelectionChange(selection) {
  ids.value = selection.map(item => item.id);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
}



/** 删除按钮操作 */
function handleDelete(row) {
  const _ids = row.id || ids.value;
  let confirmMessage = '';

  if (row && row.phoneNumber) {
    // 单个删除，显示手机号
    confirmMessage = `确认删除手机号为"${row.phoneNumber}"的用户吗？`;
  } else {
    // 批量删除，显示通用提示
    confirmMessage = '确认删除选中的用户吗？';
  }

  proxy.$modal.confirm(confirmMessage).then(function() {
    return delWxUser(_ids);
  }).then(() => {
    getList();
    proxy.$modal.msgSuccess("删除成功");
  }).catch(() => {});
}

/** 导出按钮操作 */
function handleExport() {
  proxy.download('owner/wxuser/export', {
    ...queryParams.value
  }, `wxuser_${new Date().getTime()}.xlsx`)
}

/** 批量启用按钮操作 */
function handleBatchEnable() {
  const userIds = ids.value;
  proxy.$modal.confirm('是否确认启用选中的用户？').then(function() {
    return batchEnableWxUser(userIds);
  }).then(() => {
    getList();
    proxy.$modal.msgSuccess("启用成功");
  }).catch(() => {});
}

/** 批量停用按钮操作 */
function handleBatchDisable() {
  const userIds = ids.value;
  proxy.$modal.confirm('是否确认停用选中的用户？').then(function() {
    return batchDisableWxUser(userIds);
  }).then(() => {
    getList();
    proxy.$modal.msgSuccess("停用成功");
  }).catch(() => {});
}

/** 查询车辆按钮操作 */
function handleViewCars(row) {
  getUserCars(row.id).then(response => {
    carList.value = response.data;
    carOpen.value = true;
  });
}

getList();
</script>

<style scoped>
/* 表格样式优化 */
.el-table {
  border-radius: 4px;
  overflow: hidden;
}

.el-table th {
  background-color: #fafafa;
  color: #606266;
  font-weight: 500;
}

.el-table td {
  border-bottom: 1px solid #ebeef5;
}

.el-table tr:hover td {
  background-color: #f5f7fa;
}

.el-tag {
  border-radius: 12px;
  font-size: 12px;
  padding: 0 8px;
  height: 24px;
  line-height: 22px;
}

/* 表单样式 */
.el-form-item {
  margin-bottom: 18px;
}

/* 查询表单样式 */
.el-form--inline .el-form-item {
  margin-right: 15px;
  margin-bottom: 15px;
}

/* 按钮样式 */
.el-button--text {
  padding: 0;
  margin-right: 8px;
}

.el-button--text:last-child {
  margin-right: 0;
}

/* 头像样式优化 */
.el-image {
  border: 2px solid #f0f0f0;
  transition: border-color 0.3s ease;
}

.el-image:hover {
  border-color: #409eff;
}

/* 对话框样式 */
.dialog-footer {
  text-align: right;
  padding: 20px 0 0;
}

.dialog-footer .el-button {
  margin-left: 10px;
}
</style>
