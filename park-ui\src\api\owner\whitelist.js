import request from '@/utils/request'

// 查询白名单管理列表
export function listWhitelist(query) {
  return request({
    url: '/system/owner/whitelist/list',
    method: 'get',
    params: query
  })
}

// 查询白名单管理详细
export function getWhitelist(id) {
  return request({
    url: '/system/owner/whitelist/' + id,
    method: 'get'
  })
}

// 新增白名单管理
export function addWhitelist(data) {
  return request({
    url: '/system/owner/whitelist',
    method: 'post',
    data: data
  })
}

// 修改白名单管理
export function updateWhitelist(data) {
  return request({
    url: '/system/owner/whitelist',
    method: 'put',
    data: data
  })
}

// 删除白名单管理
export function delWhitelist(id) {
  return request({
    url: '/system/owner/whitelist/' + id,
    method: 'delete'
  })
}

// 获取场库选项（包含层级关系）
export function getWarehouseOptions() {
  return request({
    url: '/system/platform/warehouse/optionSelect',
    method: 'get'
  })
}

// 获取运营商选项
export function getOperatorOptions() {
  return request({
    url: '/system/platform/operator/optionSelect',
    method: 'get'
  })
}

// 根据运营商ID获取场库选项
export function getWarehouseOptionsByOperator(operatorId) {
  return request({
    url: '/system/platform/warehouse/optionSelectByOperator',
    method: 'get',
    params: { operatorId }
  })
}
