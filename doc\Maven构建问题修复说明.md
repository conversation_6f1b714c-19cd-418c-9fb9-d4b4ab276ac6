# Maven构建问题修复说明

## 问题描述

在Maven构建过程中遇到以下错误和警告：

1. **错误**：`systemPath` 必须指定绝对路径，但当前使用的是相对路径
   - `com.sun:tools:jar` 的 `systemPath` 为 `${project.basedir}/lib/openjdk-1.8-tools.jar`
   - `com.sun:jconsole:jar` 的 `systemPath` 为 `${project.basedir}/lib/openjdk-1.8-jconsole.jar`

2. **警告**：Druid依赖的POM无效，传递依赖不可用

3. **警告**：JAR文件为空（lgjy-common-seata模块）

## 修复方案

### 1. 排除有问题的依赖

在主POM文件中为Druid依赖添加排除配置：

```xml
<!-- Druid 数据库连接池 -->
<dependency>
    <groupId>com.alibaba</groupId>
    <artifactId>druid</artifactId>
    <version>${druid.version}</version>
    <exclusions>
        <exclusion>
            <groupId>com.sun</groupId>
            <artifactId>tools</artifactId>
        </exclusion>
        <exclusion>
            <groupId>com.sun</groupId>
            <artifactId>jconsole</artifactId>
        </exclusion>
    </exclusions>
</dependency>

<!-- Druid Spring Boot Starter -->
<dependency>
    <groupId>com.alibaba</groupId>
    <artifactId>druid-spring-boot-starter</artifactId>
    <version>${druid.version}</version>
    <exclusions>
        <exclusion>
            <groupId>com.sun</groupId>
            <artifactId>tools</artifactId>
        </exclusion>
        <exclusion>
            <groupId>com.sun</groupId>
            <artifactId>jconsole</artifactId>
        </exclusion>
    </exclusions>
</dependency>
```

### 2. 更新Druid版本

将Druid版本从 `1.2.23` 更新到 `1.2.24`：

```xml
<druid.version>1.2.24</druid.version>
```

### 3. 修复空JAR问题

为 `lgjy-common-seata` 模块添加基本内容：

- 创建 `SeataAutoConfiguration.java` 配置类
- 创建 `spring.factories` 自动配置文件

### 4. 清理和重新构建

执行以下步骤清理缓存并重新构建：

#### 方法1：使用提供的批处理脚本
```bash
# Windows
clean-build.bat
```

#### 方法2：手动执行命令
```bash
# 1. 清理项目
mvn clean

# 2. 删除有问题的缓存（可选）
# Windows: 删除 %USERPROFILE%\.m2\repository\com\alibaba\druid\1.2.6
# Linux/Mac: rm -rf ~/.m2/repository/com/alibaba/druid/1.2.6

# 3. 强制更新依赖并编译
mvn compile -U

# 4. 安装到本地仓库
mvn install -DskipTests
```

## 修复后的效果

1. ✅ 解决了 `systemPath` 相对路径错误
2. ✅ 排除了有问题的 `com.sun:tools` 和 `com.sun:jconsole` 依赖
3. ✅ 修复了空JAR警告
4. ✅ 使用了更新的Druid版本，避免已知问题

## 验证修复

重新运行Maven构建命令：

```bash
mvn clean install -DskipTests
```

如果仍有问题，可以尝试：

1. 删除整个 `.m2/repository/com/alibaba/druid` 目录
2. 重新下载依赖：`mvn dependency:resolve -U`
3. 检查网络连接和Maven仓库配置

## 注意事项

1. 这些修复主要针对Druid依赖中的JDK工具类依赖问题
2. 排除的依赖（tools.jar和jconsole.jar）通常在运行时环境中已经可用
3. 如果项目确实需要这些依赖，可能需要使用其他方式引入
4. 建议定期更新依赖版本以避免类似问题

## 相关文件修改

- `park-api/pom.xml` - 添加依赖排除和版本更新
- `park-api/lgjy-common/lgjy-common-seata/src/main/java/com/lgjy/common/seata/config/SeataAutoConfiguration.java` - 新增
- `park-api/lgjy-common/lgjy-common-seata/src/main/resources/META-INF/spring.factories` - 新增
- `park-api/clean-build.bat` - 新增清理构建脚本
