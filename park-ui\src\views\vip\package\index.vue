<template>
  <div class="app-container">
    <el-form
      :model="queryParams"
      ref="queryRef"
      :inline="true"
      v-show="showSearch"
      label-width="68px"
    >
      <el-form-item label="套餐名称" prop="packageName">
        <el-input
          v-model="queryParams.packageName"
          placeholder="请输入套餐名称"
          clearable
          style="width: 200px"
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="套餐类型" prop="packageType">
        <el-select
          v-model="queryParams.packageType"
          placeholder="套餐类型"
          clearable
          style="width: 200px"
        >
          <el-option
            v-for="dict in vip_package_type"
            :key="dict.value"
            :label="dict.label"
            :value="parseInt(dict.value)"
          />
        </el-select>
      </el-form-item>

      <el-form-item label="场库" prop="warehouseId">
        <el-cascader
          v-model="queryParams.warehouseId"
          :options="warehouseCascaderOptions"
          :props="{
            value: 'value',
            label: 'label',
            children: 'children',
            emitPath: false,
            checkStrictly: true,
            expandTrigger: 'hover'
          }"
          placeholder="请选择场库"
          style="width: 200px"
          clearable
          filterable
          :show-all-levels="false"
        />
      </el-form-item>


      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery"
          >搜索</el-button
        >
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="Plus"
          @click="handleAdd"
          v-hasPermi="['vip:package:add']"
          >新增</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="Edit"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['vip:package:edit']"
          >修改</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="Delete"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['vip:package:remove']"
          >删除</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="Download"
          @click="handleExport"
          v-hasPermi="['vip:package:export']"
          >导出</el-button
        >
      </el-col>
      <right-toolbar
        v-model:showSearch="showSearch"
        @queryTable="getList"
      ></right-toolbar>
    </el-row>

    <el-table
      v-loading="loading"
      :data="packageList"
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" width="55" align="center" />
      <!-- <el-table-column label="套餐ID" align="center" prop="id" /> -->
      <el-table-column
        label="套餐名称"
        align="center"
        prop="packageName"
        width="160"
        :show-overflow-tooltip="true"
      >
        <template #default="scope">
          <el-tag type="warning" effect="light" size="small">
            <i class="el-icon-box" style="margin-right: 4px;"></i>
            {{ scope.row.packageName }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="场库/停车场" align="center" prop="warehouseName">
        <template #default="scope">
          <div style="display: flex; align-items: center; justify-content: center;">
            <el-tag
              :type="getWarehouseTagType(scope.row)"
              effect="light"
              size="small"
              :title="scope.row.warehouseName"
            >
              <i :class="getWarehouseIcon(scope.row)"></i>
              {{ truncateText(scope.row.warehouseName, 8) }}
            </el-tag>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="套餐类型" align="center" prop="packageType">
        <template #default="scope">
          <dict-tag
            :options="vip_package_type"
            :value="scope.row.packageType"
          />
        </template>
      </el-table-column>


      <el-table-column label="套餐价格" align="center" prop="packagePrice">
        <template #default="scope">
          <span class="price-text">¥{{ scope.row.packagePrice || '0.00' }}</span>
        </template>
      </el-table-column>
      <el-table-column label="会员时长" align="center" prop="packageType">
        <template #default="scope">
          <el-tag v-if="scope.row.packageType === 30" type="warning" effect="light" size="small">
            包月
          </el-tag>
          <el-tag v-else-if="scope.row.packageType === 365" type="success" effect="light" size="small">
            包年
          </el-tag>
          <el-tag v-else type="primary" effect="light" size="small">
            {{ scope.row.packageType }}天
          </el-tag>
        </template>
      </el-table-column>



      <el-table-column
        label="操作"
        align="center"
        class-name="small-padding fixed-width"
      >
        <template #default="scope">
          <el-button
            link
            type="primary"
            icon="Edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['vip:package:edit']"
            >修改</el-button
          >
          <el-button
            link
            type="primary"
            icon="Delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['vip:package:remove']"
            >删除</el-button
          >
        </template>
      </el-table-column>
    </el-table>

    <custom-pagination
      :total="total"
      v-model:current-page="queryParams.pageNum"
      v-model:page-size="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改会员套餐配置对话框 -->
    <el-dialog
      :title="title"
      v-model="open"
      width="600px"
      append-to-body
      :close-on-click-modal="false"
      :close-on-press-escape="false"
    >
      <el-form
        ref="packageRef"
        :model="form"
        :rules="rules"
        label-width="100px"
      >
        <el-row>
          <el-col :span="24">
            <el-form-item label="套餐名称" prop="packageName">
              <el-input
                v-model="form.packageName"
                placeholder="请输入套餐名称"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="场库/停车场" prop="warehouseId">
              <!-- 层级下拉选择 -->
              <el-cascader
                ref="cascaderRef"
                v-model="form.warehouseId"
                :options="warehouseCascaderOptions"
                :props="{
                  value: 'value',
                  label: 'label',
                  children: 'children',
                  emitPath: false,
                  checkStrictly: true,
                  expandTrigger: 'hover'
                }"
                placeholder="请选择场库或停车场"
                style="width: 100%"
                clearable
                filterable
                :show-all-levels="false"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="套餐类型" prop="packageType">
              <el-select
                v-model="form.packageType"
                placeholder="请选择套餐类型"
              >
                <el-option
                  v-for="dict in vip_package_type"
                  :key="dict.value"
                  :label="dict.label"
                  :value="parseInt(dict.value)"
                />
              </el-select>
            </el-form-item>
          </el-col>

        </el-row>
        <el-row>

        </el-row>
        <!-- 显示会员时长信息 -->
        <el-row v-if="form.packageType">
          <el-col :span="12">
            <el-form-item label="会员时长">
              <el-input :value="form.packageType + '天'" disabled />
            </el-form-item>
          </el-col>

        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="套餐价格" prop="packagePrice">
              <el-input-number
                v-model="form.packagePrice"
                :min="0"
                :precision="2"
                placeholder="请输入套餐价格"
              />
            </el-form-item>
          </el-col>

        </el-row>
        <el-row>


        </el-row>


        <el-form-item label="备注" prop="remark">
          <el-input
            v-model="form.remark"
            type="textarea"
            placeholder="请输入备注"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="VipPackage">
import { nextTick } from "vue";
import {
  listVipPackage,
  getVipPackage,
  delVipPackage,
  addVipPackage,
  updateVipPackage,
  getWarehouseOptions,
} from "@/api/vip/package";
import CustomPagination from "@/components/CustomPagination/index.vue";

// 注册组件
defineOptions({
  components: {
    CustomPagination,
  },
});

const { proxy } = getCurrentInstance();
const { vip_package_type } = proxy.useDict("vip_package_type");

const packageList = ref([]);
const open = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const title = ref("");

// 场库和停车场选项
const warehouseOptions = ref([]);
// 层级下拉框选项（用于cascader）
const warehouseCascaderOptions = ref([]);
// 级联选择器引用
const cascaderRef = ref();

// 移除硬编码的套餐类型选项，改用字典

const data = reactive({
  form: {},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    packageName: null,
    packageType: null,
    warehouseId: null,
  },
  rules: {
    packageName: [
      { required: true, message: "套餐名称不能为空", trigger: "blur" },
    ],
    packageType: [
      { required: true, message: "套餐类型不能为空", trigger: "change" },
    ],
    packagePrice: [
      { required: true, message: "套餐价格不能为空", trigger: "blur" },
    ],
  },
});

const { queryParams, form, rules } = toRefs(data);

// 移除硬编码的状态函数，改用字典标签组件

/** 查询会员套餐配置列表 */
function getList() {
  loading.value = true;
  listVipPackage(queryParams.value).then((response) => {
    packageList.value = response.rows;
    total.value = response.total;
    loading.value = false;
  });
}

/** 获取场库选项 */
function loadWarehouseOptions() {
  getWarehouseOptions().then((response) => {
    warehouseOptions.value = response.data;
    // 同时构建层级选项（用于cascader）
    warehouseCascaderOptions.value = buildWarehouseCascaderOptions(response.data);
  });
}

/** 构建层级下拉框选项 */
function buildWarehouseCascaderOptions(warehouses) {
  if (!warehouses || warehouses.length === 0) {
    return [];
  }

  const mainWarehouses = warehouses.filter(w => isMainWarehouse(w));
  const subWarehouses = warehouses.filter(w => !isMainWarehouse(w));

  return mainWarehouses.map(main => {
    const children = subWarehouses
      .filter(sub => sub.parentId === main.id)
      .map(sub => ({
        value: sub.id,
        label: sub.warehouseName,
        leaf: true
      }));

    return {
      value: main.id,
      label: main.warehouseName,
      children: children.length > 0 ? children : undefined
    };
  });
}

/** 根据场库ID构建完整的路径数组 */
function buildWarehousePath(warehouseId, warehouses) {
  if (!warehouseId || !warehouses || warehouses.length === 0) {
    return null;
  }

  // 转换为字符串进行比较，确保类型一致
  const targetId = String(warehouseId);

  // 查找当前场库
  const currentWarehouse = warehouses.find(w => String(w.id) === targetId);
  if (!currentWarehouse) {
    return null;
  }

  // 如果是主场库，直接返回ID
  if (isMainWarehouse(currentWarehouse)) {
    return [currentWarehouse.id];
  }

  // 如果是子场库，需要找到父场库
  const parentWarehouse = warehouses.find(w => String(w.id) === String(currentWarehouse.parentId));
  if (parentWarehouse) {
    return [parentWarehouse.id, currentWarehouse.id];
  }

  // 如果找不到父场库，返回当前场库ID
  return [currentWarehouse.id];
}

/** 判断是否为主场库 */
function isMainWarehouse(warehouse) {
  // 处理parentId为字符串"0"、数字0、null、undefined的情况
  const parentId = warehouse.parentId;
  // 主场库：parentId为0、"0"、null、undefined
  // 子场库：parentId为其他值（非0的数字或字符串）
  return parentId === 0 || parentId === "0" || parentId === null || parentId === undefined;
}

/** 获取场库标签类型 */
function getWarehouseTagType(warehouse) {
  if (isMainWarehouse(warehouse)) {
    return 'primary'; // 主场库用蓝色
  } else {
    return 'success'; // 子场库用绿色
  }
}

/** 获取场库图标 */
function getWarehouseIcon(warehouse) {
  if (isMainWarehouse(warehouse)) {
    return 'el-icon-office-building'; // 主场库用建筑图标
  } else {
    return 'el-icon-location'; // 子停车场用位置图标
  }
}

/** 文字截断函数 */
function truncateText(text, maxLength) {
  if (!text) return '';
  if (text.length <= maxLength) return text;
  return text.substring(0, maxLength) + '...';
}

/** 获取场库显示名称（包含层级关系） */
function getWarehouseDisplayName(warehouse) {
  if (isMainWarehouse(warehouse)) {
    // 主场库
    return `🏢 ${warehouse.warehouseName} (主场库)`;
  } else {
    // 子停车场，需要找到父级场库名称
    const parentWarehouse = warehouseOptions.value.find(w => w.id === warehouse.parentId);
    const parentName = parentWarehouse ? parentWarehouse.warehouseName : '主场库';
    return `🅿️ ${warehouse.warehouseName} (${parentName})`;
  }
}

// 取消按钮
function cancel() {
  open.value = false;
  reset();
}

// 表单重置
function reset() {
  form.value = {
    id: null,
    warehouseId: null,
    packageName: null,
    packageType: null,
    packagePrice: null,

    remark: null,
  };
  proxy.resetForm("packageRef");
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryRef");
  // 重置查询参数到初始状态
  Object.assign(queryParams.value, {
    pageNum: 1,
    pageSize: 10,
    packageName: null,
    packageType: null,
    warehouseId: null
  });
  handleQuery();
}

// 多选框选中数据
function handleSelectionChange(selection) {
  ids.value = selection.map((item) => item.id);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
}

/** 新增按钮操作 */
function handleAdd() {
  reset();

  // 确保场库选项已加载
  if (warehouseCascaderOptions.value.length === 0) {
    loadWarehouseOptions();
  }

  open.value = true;
  title.value = "添加会员套餐配置";
}

/** 修改按钮操作 */
function handleUpdate(row) {
  reset();
  const _id = row.id || ids.value;

  // 先确保场库选项已加载，然后获取套餐数据
  Promise.all([
    getWarehouseOptions(),
    getVipPackage(_id)
  ]).then(([warehouseResponse, packageResponse]) => {
    // 构建级联选择器选项
    warehouseOptions.value = warehouseResponse.data;
    warehouseCascaderOptions.value = buildWarehouseCascaderOptions(warehouseResponse.data);



    // 先打开对话框
    open.value = true;
    title.value = "修改会员套餐配置";

    // 使用 nextTick 确保级联选择器组件已渲染，然后设置表单数据
    nextTick(() => {
      form.value = packageResponse.data;

      // 再次使用 nextTick 确保数据绑定完成，然后处理级联选择器回显
      nextTick(() => {
        // 根据 warehouseId 构建完整的路径数组来实现回显
        if (form.value.warehouseId) {
          const warehousePath = buildWarehousePath(form.value.warehouseId, warehouseResponse.data);

          if (warehousePath && warehousePath.length > 0) {
            // 设置完整的路径数组来实现回显
            form.value.warehouseId = warehousePath;
          }
        }
      });
    });
  }).catch((error) => {
    console.error('加载数据失败:', error);
    proxy.$modal.msgError("加载数据失败");
  });
}







/** 提交按钮 */
function submitForm() {
  proxy.$refs["packageRef"].validate((valid) => {
    if (valid) {
      if (form.value.id != null) {
        updateVipPackage(form.value).then((response) => {
          proxy.$modal.msgSuccess("修改成功");
          open.value = false;
          getList();
        });
      } else {
        addVipPackage(form.value).then((response) => {
          proxy.$modal.msgSuccess("新增成功");
          open.value = false;
          getList();
        });
      }
    }
  });
}

/** 删除按钮操作 */
function handleDelete(row) {
  const _ids = row.id || ids.value;
  proxy.$modal
    .confirm('是否确认删除会员套餐配置编号为"' + _ids + '"的数据项？')
    .then(function () {
      return delVipPackage(_ids);
    })
    .then(() => {
      getList();
      proxy.$modal.msgSuccess("删除成功");
    })
    .catch(() => {});
}

/** 导出按钮操作 */
function handleExport() {
  proxy.download(
    "system/vip/package/export",
    {
      ...queryParams.value,
    },
    `vip_package_${new Date().getTime()}.xlsx`
  );
}

getList();
loadWarehouseOptions();
</script>

<style scoped>
/* 表格样式优化 */
.el-table {
  border-radius: 4px;
  overflow: hidden;
}

.el-table th {
  background-color: #fafafa;
  color: #606266;
  font-weight: 500;
}

.el-table td {
  border-bottom: 1px solid #ebeef5;
}

.el-table tr:hover td {
  background-color: #f5f7fa;
}

.el-tag {
  border-radius: 12px;
  font-size: 12px;
  padding: 0 8px;
  height: 24px;
  line-height: 22px;
}

/* 价格样式 */
.price-text {
  color: #e6a23c;
  font-weight: 600;
  font-size: 14px;
}

/* 表单样式 */
.el-form-item {
  margin-bottom: 18px;
}

.el-divider {
  margin: 24px 0;
}

.el-divider__text {
  font-weight: 600;
  color: #303133;
}

/* 查询表单样式 */
.el-form--inline .el-form-item {
  margin-right: 15px;
  margin-bottom: 15px;
}

/* 按钮样式 */
.el-button--text {
  padding: 0;
  margin-right: 8px;
}

.el-button--text:last-child {
  margin-right: 0;
}



/* 卡片样式 */
.el-card {
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.el-card__header {
  background-color: #fafafa;
  border-bottom: 1px solid #ebeef5;
}
</style>
