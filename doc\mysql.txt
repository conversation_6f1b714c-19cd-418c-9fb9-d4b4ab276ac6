根据你的错误信息和提供的知识库内容，问题主要由以下两个原因导致：

---

### **1. `mysql_native_password` 插件未加载（错误 1524）**
MySQL 8.4 默认使用 `caching_sha2_password` 作为身份验证插件，而 `mysql_native_password` 可能未被加载或已弃用 。  
**解决方案：**  
- **方法1：改用默认插件（推荐）**  
  ```sql
  CREATE USER 'root'@'%' IDENTIFIED WITH caching_sha2_password BY '1234';
  ```
  MySQL 8.4 默认启用 `caching_sha2_password`，无需额外配置 。

- **方法2：强制加载 `mysql_native_password`（临时兼容）**  
  在 `my.ini` 中添加以下配置，重启 MySQL 服务后重试：
  ```ini
  [mysqld]
  default_authentication_plugin=mysql_native_password
  ```
  但此方法可能不适用于高版本 MySQL（如 8.4），因为官方逐步弃用旧插件 。

---

### **2. `GRANT` 命令的使用错误（错误 1410）**
`GRANT` 命令无法直接创建用户。需先通过 `CREATE USER` 创建用户，再单独执行 `GRANT` 授权 。  
**正确步骤：**  
```sql
-- 1. 创建用户（使用新插件）
CREATE USER 'root'@'%' IDENTIFIED WITH caching_sha2_password BY '1234';

-- 2. 单独授权
GRANT ALL PRIVILEGES ON *.* TO 'root'@'%' WITH GRANT OPTION;

-- 3. 刷新权限
FLUSH PRIVILEGES;
```

---

### **完整验证流程**
1. **重启 MySQL 服务**（若修改了 `my.ini`）  
   以管理员身份运行命令提示符：
   ```bash
   net stop MySQL
   net start MySQL
   ```

2. **重新连接并验证用户权限**  
   ```bash
   mysql -u root -p1234 -h 127.0.0.1
   ```
   连接后检查用户权限：
   ```sql
   SHOW GRANTS FOR 'root'@'%';
   ```

---

### **其他注意事项**
- **MySQL 8.4 的兼容性**  
  如果必须使用 `mysql_native_password`，需确认插件是否已加载：
  ```sql
  SELECT * FROM mysql.plugin WHERE name = 'mysql_native_password';
  ```
  若未加载，检查 `plugin-load-add` 配置或降级到 8.0 。

- **防火墙与远程访问**  
  确保 `bind-address=0.0.0.0` 生效，并开放 3306 端口（参考原配置中的 `my.ini`）。

---

通过以上调整，用户 `root@%` 应能成功创建并授权。若仍有问题，提供 MySQL 版本和错误日志进一步排查。